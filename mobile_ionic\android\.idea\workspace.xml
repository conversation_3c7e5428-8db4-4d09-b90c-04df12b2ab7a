<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
    <layouts>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml">
        <config>
          <theme>@style/AppTheme</theme>
        </config>
      </layout>
    </layouts>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b12e244f-9629-4393-a811-6c8eed762072" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/.tsbuildinfo" beforeDir="false" afterPath="$PROJECT_DIR$/../.angular/cache/18.2.20/app/.tsbuildinfo" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/../.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/xml/file_paths.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/xml/file_paths.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../angular.json" beforeDir="false" afterPath="$PROJECT_DIR$/../angular.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/earthquake-map.page.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/earthquake-map.page.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/fire-map.page.html" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/fire-map.page.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/fire-map.page.scss" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/fire-map.page.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/fire-map.page.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/fire-map.page.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/flood-map.page.html" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/flood-map.page.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/flood-map.page.scss" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/flood-map.page.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/flood-map.page.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/flood-map.page.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/landslide-map.page.html" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/landslide-map.page.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/landslide-map.page.scss" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/landslide-map.page.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/landslide-map.page.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/landslide-map.page.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/typhoon-map.page.html" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/typhoon-map.page.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/typhoon-map.page.scss" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/typhoon-map.page.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/disaster-maps/typhoon-map.page.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/disaster-maps/typhoon-map.page.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../src/app/pages/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/../src/app/pages/home/<USER>" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2yYIjU03jUuQPUf8rPfzdTKys48" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Android App.app.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;Rolynne&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="android.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b12e244f-9629-4393-a811-6c8eed762072" name="Changes" comment="" />
      <created>1750005217481</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750005217481</updated>
    </task>
    <servers />
  </component>
  <component name="play_dynamic_filters_status">
    <option name="appIdToCheckInfo">
      <map>
        <entry key="io.ionic.starter">
          <value>
            <CheckInfo lastCheckTimestamp="1750854380794" />
          </value>
        </entry>
        <entry key="io.ionic.starter.test">
          <value>
            <CheckInfo lastCheckTimestamp="1750854380794" />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>