import{b as R,c as x}from"./chunk-YZWAW4SM.js";import{a as E}from"./chunk-I4SN7ED3.js";import{a as y}from"./chunk-3J7GGTVR.js";import{a as h,b as k}from"./chunk-2LL5MXLB.js";import{dc as D,fc as T,i as L,k as S}from"./chunk-SFXIJNIZ.js";import{f as M,g as d}from"./chunk-2R6CW7ES.js";var v=k("Filesystem",{web:()=>import("./chunk-JMTEMTXM.js").then(m=>new m.FilesystemWeb)});E();var b=M(R()),g=M(x());var j=(()=>{class m{constructor(t,o){this.toastCtrl=t,this.loadingCtrl=o}downloadMapWithRoutes(t,o,e,n=!0){return d(this,null,function*(){let r=yield this.loadingCtrl.create({message:`Capturing ${e} map with routes...`,spinner:"crescent"});yield r.present();try{let a;try{console.log("Attempting composite canvas approach...");let l=yield this.extractMapData(o);a=yield this.createCompositeMapCanvas(t,o,l)}catch(l){console.warn("Composite approach failed, trying fallback:",l),a=yield this.captureWithEnhancedHtml2Canvas(t)}let s=yield this.saveToDevice(a,e);yield r.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F4F1} ${e} map with routes saved to device gallery!`,duration:4e3,color:"success",buttons:[{text:"View",handler:()=>{this.openDeviceGallery()}}]})).present()}catch(a){yield r.dismiss(),console.error("Enhanced download error:",a),yield(yield this.toastCtrl.create({message:"Failed to save map to device. Please try again.",duration:3e3,color:"danger"})).present()}})}extractMapData(t){return d(this,null,function*(){let o=[],e=[];return t.eachLayer(n=>{if(n instanceof g.Marker){let r=n.getLatLng(),a=n.options.icon,s=[30,30];a?.options?.iconSize&&(Array.isArray(a.options.iconSize)?s=a.options.iconSize:a.options.iconSize instanceof g.Point&&(s=[a.options.iconSize.x,a.options.iconSize.y]));let i,l=n.getPopup();if(l){let c=l.getContent();typeof c=="string"?i=c:c instanceof HTMLElement&&(i=c.innerHTML)}o.push({lat:r.lat,lng:r.lng,iconUrl:a?.options?.iconUrl||"assets/Location.png",iconSize:s,popupContent:i})}else if(n instanceof g.Polyline){let r=n.getLatLngs();e.push({coordinates:r.map(a=>[a.lat,a.lng]),color:n.options.color||"#3388ff",weight:n.options.weight||3})}}),{markers:o,routes:e,bounds:t.getBounds()}})}createCompositeMapCanvas(t,o,e){return d(this,null,function*(){let n=document.getElementById(t);if(!n)throw new Error("Map element not found");console.log("Creating composite map canvas...");let r=yield this.captureBaseMap(n),a=document.createElement("canvas"),s=a.getContext("2d");if(!s)throw new Error("Could not get canvas context");let i=n.getBoundingClientRect();return a.width=i.width*2,a.height=i.height*2,s.scale(2,2),s.drawImage(r,0,0,i.width,i.height),yield this.drawRoutesOnCanvas(s,o,e.routes,i),yield this.drawMarkersOnCanvas(s,o,e.markers,i),a})}captureBaseMap(t){return d(this,null,function*(){return yield(0,b.default)(t,{useCORS:!0,allowTaint:!0,foreignObjectRendering:!1,scrollX:0,scrollY:0,scale:1,backgroundColor:"#ffffff",logging:!1,imageTimeout:1e4,ignoreElements:o=>o.classList.contains("leaflet-control-zoom")||o.classList.contains("leaflet-control-attribution")||o.classList.contains("leaflet-marker-pane")||o.classList.contains("leaflet-overlay-pane")||o.classList.contains("leaflet-shadow-pane")})})}drawRoutesOnCanvas(t,o,e,n){return d(this,null,function*(){e.forEach(r=>{r.coordinates.length<2||(t.strokeStyle=r.color,t.lineWidth=r.weight,t.lineCap="round",t.lineJoin="round",t.beginPath(),r.coordinates.forEach((a,s)=>{let i=o.latLngToContainerPoint([a[0],a[1]]);s===0?t.moveTo(i.x,i.y):t.lineTo(i.x,i.y)}),t.stroke())})})}drawMarkersOnCanvas(t,o,e,n){return d(this,null,function*(){for(let r of e)try{let a=o.latLngToContainerPoint([r.lat,r.lng]),s=yield this.loadImage(r.iconUrl),[i,l]=r.iconSize;t.drawImage(s,a.x-i/2,a.y-l,i,l)}catch(a){console.warn("Failed to load marker image:",r.iconUrl,a);let s=o.latLngToContainerPoint([r.lat,r.lng]);t.fillStyle="#ff0000",t.beginPath(),t.arc(s.x,s.y,8,0,2*Math.PI),t.fill()}})}loadImage(t){return new Promise((o,e)=>{let n=new Image;n.crossOrigin="anonymous",n.onload=()=>o(n),n.onerror=e,n.src=t})}captureWithEnhancedHtml2Canvas(t){return d(this,null,function*(){let o=document.getElementById(t);if(!o)throw new Error("Map element not found");return console.log("Using enhanced html2canvas fallback..."),yield this.waitForMapRender(),yield(0,b.default)(o,{useCORS:!0,allowTaint:!0,foreignObjectRendering:!0,scrollX:0,scrollY:0,windowWidth:window.innerWidth,windowHeight:window.innerHeight,scale:2,backgroundColor:"#ffffff",logging:!1,imageTimeout:15e3,removeContainer:!1,ignoreElements:e=>e.classList.contains("leaflet-control-zoom")||e.classList.contains("leaflet-control-attribution")})})}waitForMapRender(){return d(this,null,function*(){return new Promise(t=>{setTimeout(()=>{requestAnimationFrame(()=>{setTimeout(t,500)})},1e3)})})}createOfflineMapCanvas(t,o,e=800,n=600){return d(this,null,function*(){let r=document.createElement("canvas"),a=r.getContext("2d");if(!a)throw new Error("Could not get canvas context");r.width=e,r.height=n,a.fillStyle="#f0f0f0",a.fillRect(0,0,e,n);try{let s=t.getBounds(),i=t.getZoom(),l=t.getCenter();yield this.drawCachedTiles(a,s,i,e,n);let c=document.createElement("div");c.style.width=`${e}px`,c.style.height=`${n}px`,c.style.position="absolute",c.style.top="-9999px",document.body.appendChild(c);let w=g.map(c).setView([l.lat,l.lng],i);yield this.drawRoutesOnCanvas(a,w,o.routes,{width:e,height:n}),yield this.drawMarkersOnCanvas(a,w,o.markers,{width:e,height:n}),w.remove(),document.body.removeChild(c)}catch(s){console.warn("Error creating offline map canvas:",s),a.fillStyle="#666",a.font="16px Arial",a.textAlign="center",a.fillText("Map data unavailable",e/2,n/2)}return r})}drawCachedTiles(t,o,e,n,r){return d(this,null,function*(){let s=o.getNorthWest(),i=o.getSouthEast(),l=this.latLngToTile(s.lat,s.lng,e),c=this.latLngToTile(i.lat,i.lng,e),w=Math.floor(l.x),P=Math.ceil(c.x),z=Math.floor(l.y),F=Math.ceil(c.y);for(let f=w;f<=P;f++)for(let u=z;u<=F;u++)try{let p=(f-l.x)*256,C=(u-l.y)*256;t.fillStyle="#e8f4f8",t.fillRect(p,C,256,256),t.strokeStyle="#b0d4e3",t.strokeRect(p,C,256,256),t.fillStyle="#666",t.font="10px Arial",t.textAlign="center",t.fillText("Map Area",p+256/2,C+256/2)}catch(p){console.warn(`Failed to draw tile ${e}/${f}/${u}:`,p)}})}latLngToTile(t,o,e){let n=Math.pow(2,e),r=(o+180)/360*n,a=(1-Math.log(Math.tan(t*Math.PI/180)+1/Math.cos(t*Math.PI/180))/Math.PI)/2*n;return{x:r,y:a}}loadImageFromBase64(t){return new Promise((o,e)=>{let n=new Image;n.onload=()=>o(n),n.onerror=e,n.src=`data:image/png;base64,${t}`})}saveToDevice(t,o){return d(this,null,function*(){let e=t.toDataURL("image/png",1),n=e.split(",")[1],a=new Date().toISOString().replace(/[:.]/g,"-").substring(0,19),s=`${o.toLowerCase()}-evacuation-map-${a}.png`;try{return h.isNativePlatform()?yield this.saveToNativeDevice(n,s):this.fallbackBrowserDownload(e,s),s}catch(i){return console.error("Download error:",i),this.fallbackBrowserDownload(e,s),s}})}saveToNativeDevice(t,o){return d(this,null,function*(){try{let e=yield v.writeFile({path:o,data:t,directory:y.Documents,recursive:!0});if(console.log("File saved to:",e.uri),this.isAndroid())try{yield v.writeFile({path:`Download/${o}`,data:t,directory:y.ExternalStorage,recursive:!0}),console.log("File also saved to Downloads folder")}catch(n){console.log("Could not save to Downloads folder:",n)}}catch(e){throw console.error("Native file save error:",e),e}})}fallbackBrowserDownload(t,o){let e=document.createElement("a");e.href=t,e.download=o,document.body.appendChild(e),e.click(),document.body.removeChild(e),console.log("Used fallback browser download")}isAndroid(){return/Android/i.test(navigator.userAgent)}isIOS(){return/iPad|iPhone|iPod/.test(navigator.userAgent)}openDeviceGallery(){return d(this,null,function*(){try{if(h.isNativePlatform())if(this.isAndroid())try{window.open("content://com.android.externalstorage.documents/document/primary%3ADownload","_system")}catch{window.open("content://com.android.externalstorage.documents/","_system")}else this.isIOS()&&window.open("shareddocuments://","_system");else yield(yield this.toastCtrl.create({message:"File downloaded to your browser's download folder",duration:3e3,color:"primary"})).present()}catch(t){console.error("Error opening gallery:",t),yield(yield this.toastCtrl.create({message:"File saved successfully! Check your device's file manager.",duration:3e3,color:"success"})).present()}})}getDownloadStats(){return d(this,null,function*(){try{if(h.isNativePlatform()){let o=(yield v.readdir({path:"",directory:y.Documents})).files.filter(e=>e.name.includes("evacuation-map")&&e.name.endsWith(".png"));return{totalDownloads:o.length,lastDownload:o.length>0?o[o.length-1].name:"None"}}else return{totalDownloads:0,lastDownload:"Check browser downloads"}}catch(t){return console.error("Error getting download stats:",t),{totalDownloads:0,lastDownload:"None"}}})}static{this.\u0275fac=function(o){return new(o||m)(S(T),S(D))}}static{this.\u0275prov=L({token:m,factory:m.\u0275fac,providedIn:"root"})}}return m})();export{j as a};
