import{a as ut}from"./chunk-OEBCGVFF.js";import{a as dt,c as _t}from"./chunk-YZWAW4SM.js";import{a as mt}from"./chunk-QZ5PEPJK.js";import{b as gt}from"./chunk-I4SN7ED3.js";import{a as pt}from"./chunk-LSM7X32V.js";import"./chunk-3J7GGTVR.js";import{a as lt}from"./chunk-WPPT3EJF.js";import"./chunk-2LL5MXLB.js";import{$ as E,B as s,Eb as X,F as P,G as _,I as b,Ib as Z,J as i,Jb as tt,K as r,L as u,Mb as et,O as N,P as f,Q as m,Sb as nt,Tb as ot,X as l,Y as C,Z as k,Zb as it,aa as A,ba as z,bc as at,ca as D,cb as B,d as L,dc as rt,fb as W,fc as st,gc as ct,ib as G,l as x,m as R,ma as $,na as F,pa as H,r as y,ra as Y,s as w,sb as J,wa as V,wb as j,xb as K,ya as U,yb as q,zb as Q}from"./chunk-SFXIJNIZ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{a as I,b as S,f as ht,g as h}from"./chunk-2R6CW7ES.js";var g=ht(_t());function ft(c,v){if(c&1&&(i(0,"div",44)(1,"ion-card")(2,"ion-card-content")(3,"div",45),u(4,"ion-icon",46),i(5,"span"),l(6,"Route to Nearest Center"),r()(),i(7,"div",40)(8,"div",47),u(9,"ion-icon",39),i(10,"span"),l(11),r()(),i(12,"div",47),u(13,"ion-icon",48),i(14,"span"),l(15),r()()()()()()),c&2){let t=m();s(9),_("name",t.travelMode==="walking"?"walk-outline":t.travelMode==="cycling"?"bicycle-outline":"car-outline"),s(2),k("",(t.routeTime/60).toFixed(0)," min"),s(4),k("",(t.routeDistance/1e3).toFixed(2)," km")}}function Ct(c,v){if(c&1&&(i(0,"span",57),l(1),r()),c&2){let t=m().$implicit,o=m();s(),k(" \u{1F4CD} ",o.calculateDistanceInKm(t)," km away ")}}function Mt(c,v){if(c&1){let t=N();i(0,"div",49),f("click",function(){let e=y(t).$implicit,n=m();return w(n.selectCenterFromList(e))}),i(1,"div",50)(2,"h4"),l(3),r(),i(4,"p",51),l(5),r(),i(6,"div",52),P(7,Ct,2,1,"span",53),i(8,"span",54),l(9),r()()(),i(10,"div",55),u(11,"ion-icon",56),r()()}if(c&2){let t=v.$implicit,o=m();s(3),C(t.name),s(2),C(t.address),s(2),_("ngIf",o.userLocation),s(2),k("\u{1F465} ",t.capacity||"N/A"," capacity")}}function Pt(c,v){if(c&1){let t=N();i(0,"app-real-time-navigation",58),f("routeUpdated",function(e){y(t);let n=m();return w(n.onNavigationRouteUpdated(e))})("navigationStopped",function(){y(t);let e=m();return w(e.onNavigationStopped())}),r()}if(c&2){let t=m();_("destination",t.navigationDestination)("travelMode",t.selectedTransportMode==="walking"?"foot-walking":t.selectedTransportMode==="cycling"?"cycling-regular":"driving-car")("autoStart",t.isRealTimeNavigationActive)}}function vt(c,v){if(c&1&&(i(0,"div",50)(1,"h3"),l(2),r(),i(3,"p"),l(4),r()()),c&2){let t=m();s(2),C(t.selectedCenter.name),s(2),C(t.selectedCenter.address)}}function Ot(c,v){if(c&1&&(i(0,"div",44)(1,"span",65),l(2),r(),i(3,"span",57),l(4),r()()),c&2){let t=m(2);s(2),C(t.formatTime(t.routeInfo.walking==null?null:t.routeInfo.walking.duration)),s(2),C(t.formatDistance(t.routeInfo.walking==null?null:t.routeInfo.walking.distance))}}function xt(c,v){if(c&1&&(i(0,"div",44)(1,"span",65),l(2),r(),i(3,"span",57),l(4),r()()),c&2){let t=m(2);s(2),C(t.formatTime(t.routeInfo.cycling.duration)),s(2),C(t.formatDistance(t.routeInfo.cycling.distance))}}function yt(c,v){if(c&1&&(i(0,"div",44)(1,"span",65),l(2),r(),i(3,"span",57),l(4),r()()),c&2){let t=m(2);s(2),C(t.formatTime(t.routeInfo.driving==null?null:t.routeInfo.driving.duration)),s(2),C(t.formatDistance(t.routeInfo.driving==null?null:t.routeInfo.driving.distance))}}function wt(c,v){if(c&1){let t=N();i(0,"button",66),f("click",function(){y(t);let e=m(2);return w(e.startRealTimeNavigation(e.selectedCenter))}),u(1,"ion-icon",61),l(2," Start Navigation "),r()}}function bt(c,v){if(c&1){let t=N();i(0,"div",59)(1,"div",60),u(2,"ion-icon",61),i(3,"span"),l(4,"Choose Transportation"),r()(),i(5,"div",62)(6,"button",63),f("click",function(){y(t);let e=m();return w(e.selectTransportMode("walking"))}),u(7,"ion-icon",15),i(8,"span"),l(9,"Walk"),r(),P(10,Ot,5,2,"div",20),r(),i(11,"button",63),f("click",function(){y(t);let e=m();return w(e.selectTransportMode("cycling"))}),u(12,"ion-icon",17),i(13,"span"),l(14,"Cycle"),r(),P(15,xt,5,2,"div",20),r(),i(16,"button",63),f("click",function(){y(t);let e=m();return w(e.selectTransportMode("driving"))}),u(17,"ion-icon",19),i(18,"span"),l(19,"Drive"),r(),P(20,yt,5,2,"div",20),r()(),P(21,wt,3,0,"button",64),r()}if(c&2){let t=m();s(6),b("active",t.selectedTransportMode==="walking"),s(4),_("ngIf",t.routeInfo&&t.selectedTransportMode==="walking"),s(),b("active",t.selectedTransportMode==="cycling"),s(4),_("ngIf",t.routeInfo.cycling),s(),b("active",t.selectedTransportMode==="driving"),s(4),_("ngIf",t.routeInfo&&t.selectedTransportMode==="driving"),s(),_("ngIf",t.selectedTransportMode)}}function Tt(c,v){if(c&1&&(i(0,"div",67)(1,"span",65),l(2),r(),i(3,"span",57),l(4),r()()),c&2){let t=m();s(2),C(t.formatTime(t.routeInfo[t.selectedTransportMode]==null?null:t.routeInfo[t.selectedTransportMode].duration)),s(2),C(t.formatDistance(t.routeInfo[t.selectedTransportMode]==null?null:t.routeInfo[t.selectedTransportMode].distance))}}var Jt=(()=>{class c{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.showAllCentersPanel=!1,this.showRouteFooter=!1,this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.selectedCenter=null,this.selectedTransportMode="walking",this.routeInfo={},this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.loadingCtrl=x(rt),this.toastCtrl=x(st),this.alertCtrl=x(at),this.http=x(Y),this.router=x(U),this.route=x(V),this.enhancedDownload=x(ut),this.osmRouting=x(pt),this.mapboxRouting=x(dt)}ngOnInit(){console.log("\u{1F7E2} TYPHOON MAP: Component initialized..."),this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null,console.log("\u{1F7E2} TYPHOON MAP: New center to highlight:",this.newCenterId))})}ngAfterViewInit(){return h(this,null,function*(){console.log("\u{1F7E2} TYPHOON MAP: View initialized, loading map..."),setTimeout(()=>h(this,null,function*(){yield this.loadTyphoonMap()}),100)})}loadTyphoonMap(){return h(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading typhoon evacuation centers...",spinner:"crescent"});yield t.present();try{let o=yield gt.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),e=o.coords.latitude,n=o.coords.longitude;console.log(`\u{1F7E2} TYPHOON MAP: User location [${e}, ${n}]`),this.userLocation={lat:e,lng:n},this.initializeMap(e,n),yield this.loadTyphoonCenters(e,n),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F7E2} Showing ${this.evacuationCenters.length} typhoon evacuation centers`,duration:3e3,color:"success",position:"top"})).present()}catch(o){yield t.dismiss(),console.error("\u{1F7E2} TYPHOON MAP: Error loading map",o),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadTyphoonMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,o){if(console.log(`\u{1F7E2} TYPHOON MAP: Initializing map at [${t}, ${o}]`),!document.getElementById("typhoon-map"))throw console.error("\u{1F7E2} TYPHOON MAP: Container #typhoon-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=g.map("typhoon-map").setView([t,o],13),g.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=g.marker([t,o],{icon:g.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadTyphoonCenters(t,o){return h(this,null,function*(){try{console.log("\u{1F7E2} TYPHOON MAP: Fetching typhoon centers...");let e=[];try{e=(yield L(this.http.get(`${lt.apiUrl}/evacuation-centers`))).data||[],console.log("\u{1F7E2} TYPHOON MAP: Total centers received from API:",e?.length||0)}catch(n){console.error("\u274C API failed:",n),yield(yield this.alertCtrl.create({header:"Connection Error",message:"Cannot connect to server. Please check your internet connection.",buttons:["OK"]})).present();return}if(this.evacuationCenters=e.filter(n=>Array.isArray(n.disaster_type)?n.disaster_type.some(a=>a==="Typhoon"):n.disaster_type==="Typhoon"),console.log(`\u{1F7E2} TYPHOON MAP: Filtered to ${this.evacuationCenters.length} typhoon centers`),console.log("\u{1F7E2} TYPHOON MAP: Filtered centers:",this.evacuationCenters.map(n=>`${n.name} (${JSON.stringify(n.disaster_type)})`)),console.log(`\u{1F7E2} TYPHOON MAP: Filtered to ${this.evacuationCenters.length} typhoon centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Typhoon Centers",message:"No typhoon evacuation centers found in the database.",buttons:["OK"]})).present();return}if(this.evacuationCenters.forEach(n=>{let a=Number(n.latitude),p=Number(n.longitude);if(!isNaN(a)&&!isNaN(p)){let d=g.marker([a,p],{icon:g.icon({iconUrl:"assets/forTyphoon.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),M=this.calculateDistance(t,o,a,p);d.on("click",()=>{this.openNavigationPanel(n)});let O=this.newCenterId&&n.id.toString()===this.newCenterId;d.bindPopup(`
            <div class="evacuation-popup">
              <h3>\u{1F7E2} ${n.name} ${O?"\u2B50 NEW!":""}</h3>
              <p><strong>Type:</strong> Typhoon Center</p>
              <p><strong>Distance:</strong> ${(M/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${n.capacity||"N/A"}</p>
              <p><em>Click marker for route options</em></p>
              ${O?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
            </div>
          `),O&&(d.openPopup(),this.map.setView([a,p],15),this.toastCtrl.create({message:`\u{1F195} New typhoon evacuation center: ${n.name}`,duration:5e3,color:"success",position:"top"}).then(T=>T.present())),d.addTo(this.map),console.log(`\u{1F7E2} Added typhoon marker: ${n.name}`)}}),console.log("\u{1F7E2} Auto-routing to 2 nearest typhoon centers..."),yield this.routeToTwoNearestCenters(),this.evacuationCenters.length>0){let n=g.latLngBounds([]);n.extend([t,o]),this.evacuationCenters.forEach(a=>{n.extend([Number(a.latitude),Number(a.longitude)])}),this.map.fitBounds(n,{padding:[50,50]})}}catch(e){console.error("\u{1F7E2} TYPHOON MAP: Error loading centers",e),yield(yield this.toastCtrl.create({message:"Error loading typhoon centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}routeToTwoNearestCenters(){return h(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F7E2} TYPHOON MAP: No user location or evacuation centers available");return}try{console.log("\u{1F7E2} TYPHOON MAP: Finding 2 nearest typhoon centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0){yield(yield this.toastCtrl.create({message:"No typhoon evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(t),yield this.calculateRoutes(t),yield(yield this.toastCtrl.create({message:`\u{1F7E2} Showing routes to ${t.length} nearest typhoon centers`,duration:4e3,color:"success"})).present()}catch(t){console.error("\u{1F7E2} TYPHOON MAP: Error calculating routes",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}getTwoNearestCenters(t,o){return this.evacuationCenters.map(n=>S(I({},n),{distance:this.calculateDistance(t,o,Number(n.latitude),Number(n.longitude))})).sort((n,a)=>n.distance-a.distance).slice(0,2)}addPulsingMarkers(t){this.nearestMarkers.forEach(o=>this.map.removeLayer(o)),this.nearestMarkers=[],t.forEach((o,e)=>{let n=Number(o.latitude),a=Number(o.longitude);if(!isNaN(n)&&!isNaN(a)){let p=g.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <img src="assets/forTyphoon.png" class="marker-icon" />
              <div class="marker-label">${e+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),d=g.marker([n,a],{icon:p});d.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${e+1}</h3>
            <h4>${o.name}</h4>
            <p><strong>Type:</strong> Typhoon</p>
            <p><strong>Distance:</strong> ${(o.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${o.capacity||"N/A"}</p>
          </div>
        `),d.addTo(this.map),this.nearestMarkers.push(d)}})}clearRoutes(){this.map.eachLayer(t=>{t instanceof g.GeoJSON&&this.map.removeLayer(t)})}calculateRoutes(t){return h(this,null,function*(){if(this.userLocation){this.routeLayer=g.layerGroup().addTo(this.map);for(let o=0;o<t.length;o++){let e=t[o],n=Number(e.latitude),a=Number(e.longitude);if(!isNaN(n)&&!isNaN(a))try{console.log(`\u{1F7E2} TYPHOON MAP: Creating Mapbox route to center ${o+1}: ${e.name}`);let p=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),d=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,a,n,p);if(d&&d.routes&&d.routes.length>0){let M=d.routes[0];g.polyline(M.geometry.coordinates.map(T=>[T[1],T[0]]),{color:"#2dd36f",weight:4,opacity:.8,dashArray:o===0?void 0:"10, 10"}).addTo(this.routeLayer),o===0&&(this.routeTime=M.duration,this.routeDistance=M.distance),console.log(`\u2705 TYPHOON MAP: Added Mapbox route to ${e.name} (${(M.distance/1e3).toFixed(2)}km, ${(M.duration/60).toFixed(0)}min)`)}}catch(p){console.error(`\u{1F7E2} Error calculating Mapbox route to center ${o+1}:`,p),g.polyline([[this.userLocation.lat,this.userLocation.lng],[n,a]],{color:"#2dd36f",weight:4,opacity:.8,dashArray:o===0?void 0:"10, 10"}).addTo(this.routeLayer),console.log(`\u26A0\uFE0F TYPHOON MAP: Used fallback straight-line route to ${e.name}`)}}}})}calculateRoute(t,o){return h(this,null,function*(){try{if(!this.userLocation){console.error("\u{1F7E2} TYPHOON MAP: No user location available for routing");return}let e=this.osmRouting.convertTravelModeToProfile(o),n=yield this.osmRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(n.routes&&n.routes.length>0){let a=n.routes[0],p=this.osmRouting.convertToGeoJSON(a);g.geoJSON(p,{style:{color:"#008000",weight:4,opacity:.8}}).addTo(this.map),console.log(`\u{1F7E2} TYPHOON MAP: Route added to ${t.name}`)}}catch(e){console.error("\u{1F7E2} TYPHOON MAP: Error calculating route:",e)}})}openInExternalMaps(t,o){return h(this,null,function*(){let e=Number(t.latitude),n=Number(t.longitude),a="walking";o==="driving"?a="driving":o==="cycling"&&(a="bicycling");let p=`https://www.google.com/maps/dir/?api=1&destination=${e},${n}&travelmode=${a}`;try{window.open(p,"_system")}catch(d){console.error("Error opening external maps:",d),yield(yield this.toastCtrl.create({message:"Could not open external maps app",duration:3e3,color:"danger"})).present()}})}calculateAllRoutes(t){return h(this,null,function*(){if(!this.userLocation)return;let o=["walking","cycling","driving"];for(let e of o)try{let n=this.osmRouting.convertTravelModeToProfile(e),a=yield this.osmRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),n);if(a.routes&&a.routes.length>0){let p=a.routes[0];this.routeInfo[e]={duration:p.duration,distance:p.distance}}}catch(n){console.error(`Error calculating ${e} route:`,n)}})}startNavigation(){return h(this,null,function*(){if(!this.selectedCenter||!this.selectedTransportMode)return;yield this.routeToCenter(this.selectedCenter,this.selectedTransportMode),this.closeNavigationPanel(),yield(yield this.toastCtrl.create({message:`\u{1F9ED} Navigation started to ${this.selectedCenter.name}`,duration:3e3,color:"success",position:"top"})).present()})}routeToCenter(t,o){return h(this,null,function*(){if(this.userLocation)try{this.clearRoutes();let e=this.osmRouting.convertTravelModeToProfile(o),n=yield this.osmRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(n&&n.routes&&n.routes.length>0){let a=n.routes[0],d=g.polyline(a.geometry.coordinates.map(O=>[O[1],O[0]]),{color:"#008000",weight:5,opacity:.8});d.addTo(this.map),yield(yield this.toastCtrl.create({message:`\u{1F7E2} Route: ${(a.distance/1e3).toFixed(2)}km, ${(a.duration/60).toFixed(0)}min via ${o}`,duration:4e3,color:"success"})).present(),this.map.fitBounds(d.getBounds(),{padding:[50,50]})}}catch(e){console.error("\u{1F7E2} TYPHOON MAP: Error calculating individual route:",e),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}calculateDistance(t,o,e,n){let p=t*Math.PI/180,d=e*Math.PI/180,M=(e-t)*Math.PI/180,O=(n-o)*Math.PI/180,T=Math.sin(M/2)*Math.sin(M/2)+Math.cos(p)*Math.cos(d)*Math.sin(O/2)*Math.sin(O/2);return 6371e3*(2*Math.atan2(Math.sqrt(T),Math.sqrt(1-T)))}goBack(){this.router.navigate(["/tabs/home"])}onTravelModeChange(t){let o=t.detail.value;this.changeTravelMode(o)}changeTravelMode(t){return h(this,null,function*(){this.travelMode=t,yield(yield this.toastCtrl.create({message:`\u{1F7E2} Travel mode changed to ${t}`,duration:2e3,color:"success"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}downloadMap(){return h(this,null,function*(){if(!this.map){yield(yield this.toastCtrl.create({message:"Map not loaded yet. Please wait and try again.",duration:3e3,color:"warning"})).present();return}try{yield this.enhancedDownload.downloadMapWithRoutes("typhoon-map",this.map,"Typhoon",!0)}catch(t){console.error("Enhanced download error:",t),yield(yield this.toastCtrl.create({message:"Failed to download map. Please try again.",duration:3e3,color:"danger"})).present()}})}showAllCenters(){this.showAllCentersPanel=!0}closeAllCentersPanel(){this.showAllCentersPanel=!1}routeToNearestCenters(){return h(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){yield(yield this.toastCtrl.create({message:"Unable to calculate routes. Please ensure location is available.",duration:3e3,color:"warning"})).present();return}try{this.clearRoutes(),yield this.routeToTwoNearestCenters(),yield(yield this.toastCtrl.create({message:"\u{1F7E2} Routes calculated to 2 nearest typhoon evacuation centers",duration:4e3,color:"success"})).present()}catch(t){console.error("Error calculating routes:",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}selectCenterFromList(t){this.closeAllCentersPanel(),this.openNavigationPanel(t)}calculateDistanceInKm(t){return this.userLocation?(this.calculateDistance(this.userLocation.lat,this.userLocation.lng,Number(t.latitude),Number(t.longitude))/1e3).toFixed(1):"N/A"}openNavigationPanel(t){this.selectedCenter=t,this.showRouteFooter=!0,this.calculateRouteInfo(t)}closeNavigationPanel(){this.selectedCenter=null,this.showRouteFooter=!1,this.routeInfo={}}selectTransportMode(t){this.selectedTransportMode=t,this.selectedCenter&&this.showRouteOnMap(this.selectedCenter,t)}calculateRouteInfo(t){return h(this,null,function*(){if(!this.userLocation)return;let o=["walking","cycling","driving"];for(let e of o)try{let n=this.osmRouting.convertTravelModeToProfile(e==="walking"?"foot-walking":e==="cycling"?"cycling-regular":"driving-car"),a=yield this.osmRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),n);if(a.routes&&a.routes.length>0){let p=a.routes[0];this.routeInfo[e]={duration:p.duration,distance:p.distance}}}catch(n){console.error(`Error calculating ${e} route:`,n);let a=this.calculateDistance(this.userLocation.lat,this.userLocation.lng,Number(t.latitude),Number(t.longitude));this.routeInfo[e]={duration:a/(e==="walking"?5e3:e==="cycling"?15e3:5e4)*3600,distance:a}}})}showRouteOnMap(t,o){return h(this,null,function*(){if(this.userLocation)try{let e=this.osmRouting.convertTravelModeToProfile(o==="walking"?"foot-walking":o==="cycling"?"cycling-regular":"driving-car"),n=yield this.osmRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),e);if(n.routes&&n.routes.length>0){let a=n.routes[0],p=this.osmRouting.convertToGeoJSON(a);this.clearRoutes(),g.geoJSON(p,{style:{color:"#2dd36f",weight:4,opacity:.8}}).addTo(this.map)}}catch(e){console.error("Error showing route on map:",e)}})}formatTime(t){return t?`${Math.round(t/60)} min`:"--"}formatDistance(t){if(!t)return"--";let o=t/1e3;return o<1?`${Math.round(t)} m`:`${o.toFixed(1)} km`}ionViewWillLeave(){this.isRealTimeNavigationActive&&this.osmRouting.stopRealTimeRouting(),this.map&&this.map.remove()}startRealTimeNavigation(t){console.log("\u{1F9ED} Starting real-time navigation to typhoon center:",t.name),this.navigationDestination={lat:Number(t.latitude),lng:Number(t.longitude),name:t.name},this.isRealTimeNavigationActive=!0,this.toastCtrl.create({message:`\u{1F9ED} Real-time navigation started to ${t.name}`,duration:3e3,color:"primary"}).then(o=>o.present())}onNavigationRouteUpdated(t){console.log("\u{1F504} Typhoon map navigation route updated"),this.currentNavigationRoute=t,this.updateMapWithNavigationRoute(t)}onNavigationStopped(){console.log("\u23F9\uFE0F Typhoon map real-time navigation stopped"),this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.clearNavigationRoute(),this.toastCtrl.create({message:"\u23F9\uFE0F Navigation stopped",duration:2e3,color:"medium"}).then(t=>t.present())}updateMapWithNavigationRoute(t){if(this.clearNavigationRoute(),t.geometry&&t.geometry.coordinates){let o=this.osmRouting.convertToGeoJSON(t),e=g.geoJSON(o,{style:{color:"#007bff",weight:6,opacity:.8,dashArray:"10, 5"}}).addTo(this.map);e.isNavigationRoute=!0}}clearNavigationRoute(){this.map.eachLayer(t=>{t.isNavigationRoute&&this.map.removeLayer(t)})}static{this.\u0275fac=function(o){return new(o||c)}}static{this.\u0275cmp=R({type:c,selectors:[["app-typhoon-map"]],standalone:!0,features:[D],decls:61,vars:20,consts:[[3,"translucent"],["color","success"],["slot","start"],["fill","clear",3,"click"],["name","arrow-back-outline","color","light"],[3,"fullscreen"],["id","typhoon-map",2,"height","100%","width","100%"],[1,"map-controls"],["fill","clear",1,"control-btn",3,"click"],["src","assets/home-insuranceForTyphoon.png","alt","All Centers",1,"control-icon"],["src","assets/downloadForTyphoon.png","alt","Download",1,"control-icon"],["src","assets/compassForTyphoon.png","alt","Route to Nearest",1,"control-icon"],[1,"travel-mode-selector"],[3,"ngModelChange","ionChange","ngModel"],["value","walking"],["name","walk-outline"],["value","cycling"],["name","bicycle-outline"],["value","driving"],["name","car-outline"],["class","route-info",4,"ngIf"],[1,"all-centers-panel"],[1,"panel-content"],[1,"panel-header"],[1,"header-info"],["name","close"],[1,"centers-list"],["class","center-item",3,"click",4,"ngFor","ngForOf"],[1,"all-centers-overlay",3,"click"],[3,"destination","travelMode","autoStart","routeUpdated","navigationStopped",4,"ngIf"],[1,"navigation-panel"],["class","center-info",4,"ngIf"],["fill","clear","size","small",3,"click"],["name","close-outline"],["class","transport-options",4,"ngIf"],[1,"route-footer"],[1,"footer-content"],[1,"route-summary"],[1,"transport-icon"],[3,"name"],[1,"route-details"],[1,"destination"],["class","route-info-footer",4,"ngIf"],["fill","solid","color","success",3,"click"],[1,"route-info"],[1,"route-header"],["name","time-outline","color","success"],[1,"route-item"],["name","location-outline"],[1,"center-item",3,"click"],[1,"center-info"],[1,"address"],[1,"center-details"],["class","distance",4,"ngIf"],[1,"capacity"],[1,"center-actions"],["name","chevron-forward-outline"],[1,"distance"],[3,"routeUpdated","navigationStopped","destination","travelMode","autoStart"],[1,"transport-options"],[1,"option-header"],["name","navigate-outline"],[1,"transport-buttons"],[1,"transport-btn",3,"click"],["class","start-navigation-btn",3,"click",4,"ngIf"],[1,"time"],[1,"start-navigation-btn",3,"click"],[1,"route-info-footer"]],template:function(o,e){o&1&&(i(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),f("click",function(){return e.goBack()}),u(4,"ion-icon",4),r()()()(),i(5,"ion-content",5),u(6,"div",6),i(7,"div",7)(8,"ion-button",8),f("click",function(){return e.showAllCenters()}),u(9,"img",9),r(),i(10,"ion-button",8),f("click",function(){return e.downloadMap()}),u(11,"img",10),r(),i(12,"ion-button",8),f("click",function(){return e.routeToNearestCenters()}),u(13,"img",11),r()(),i(14,"div",12)(15,"ion-segment",13),z("ngModelChange",function(a){return A(e.travelMode,a)||(e.travelMode=a),a}),f("ionChange",function(a){return e.onTravelModeChange(a)}),i(16,"ion-segment-button",14),u(17,"ion-icon",15),i(18,"ion-label"),l(19,"Walk"),r()(),i(20,"ion-segment-button",16),u(21,"ion-icon",17),i(22,"ion-label"),l(23,"Cycle"),r()(),i(24,"ion-segment-button",18),u(25,"ion-icon",19),i(26,"ion-label"),l(27,"Drive"),r()()()(),P(28,ft,16,3,"div",20),i(29,"div",21)(30,"div",22)(31,"div",23)(32,"div",24)(33,"h3"),l(34,"\u{1F7E2} Typhoon Evacuation Centers"),r(),i(35,"p"),l(36),r()(),i(37,"ion-button",3),f("click",function(){return e.closeAllCentersPanel()}),u(38,"ion-icon",25),r()(),i(39,"div",26),P(40,Mt,12,4,"div",27),r()()(),i(41,"div",28),f("click",function(){return e.closeAllCentersPanel()}),r(),P(42,Pt,1,3,"app-real-time-navigation",29),i(43,"div",30)(44,"div",22)(45,"div",23),P(46,vt,5,2,"div",31),i(47,"ion-button",32),f("click",function(){return e.closeNavigationPanel()}),u(48,"ion-icon",33),r()(),P(49,bt,22,10,"div",34),r()(),i(50,"div",35)(51,"div",36)(52,"div",37)(53,"div",38),u(54,"ion-icon",39),r(),i(55,"div",40)(56,"div",41),l(57),r(),P(58,Tt,5,2,"div",42),r()(),i(59,"ion-button",43),f("click",function(){return e.startRealTimeNavigation(e.selectedCenter)}),l(60," Start "),r()()()()),o&2&&(_("translucent",!0),s(5),_("fullscreen",!0),s(10),E("ngModel",e.travelMode),s(13),_("ngIf",e.routeTime>0&&e.routeDistance>0),s(),b("show",e.showAllCentersPanel),s(7),k("",e.evacuationCenters.length," centers available"),s(4),_("ngForOf",e.evacuationCenters),s(),b("show",e.showAllCentersPanel),s(),_("ngIf",e.navigationDestination),s(),b("show",e.selectedCenter),s(3),_("ngIf",e.selectedCenter),s(3),_("ngIf",e.selectedCenter),s(),b("show",e.showRouteFooter&&e.selectedCenter),s(4),_("name",e.selectedTransportMode==="walking"?"walk-outline":e.selectedTransportMode==="cycling"?"bicycle-outline":"car-outline"),s(3),C(e.selectedCenter==null?null:e.selectedCenter.name),s(),_("ngIf",e.routeInfo[e.selectedTransportMode]))},dependencies:[ct,j,K,q,Q,X,Z,tt,et,nt,ot,it,J,H,$,F,G,B,W,mt],styles:["#typhoon-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.map-controls[_ngcontent-%COMP%]{position:absolute;top:80px;right:10px;z-index:1000;display:flex;flex-direction:column;gap:8px}.control-btn[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .95);--color: #2dd36f;--border-radius: 12px;width:50px;height:50px;box-shadow:0 4px 12px #00000026;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(45,211,111,.2)}.control-btn[_ngcontent-%COMP%]:hover{--background: rgba(45, 211, 111, .1);transform:translateY(-2px);box-shadow:0 6px 16px #0003}.control-icon[_ngcontent-%COMP%]{width:28px;height:28px;object-fit:contain}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:250px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-success);margin-bottom:4px}.floating-info[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:400px;height:100vh;background:#fff;box-shadow:-4px 0 20px #00000026;z-index:2000;transition:right .3s ease-in-out;overflow:hidden}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.all-centers-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:20px}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px;padding-bottom:16px;border-bottom:2px solid var(--ion-color-success-tint)}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:20px;font-weight:700;color:var(--ion-color-success);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding-right:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:12px;background:#fff;border-radius:12px;border:1px solid var(--ion-color-light);cursor:pointer;transition:all .2s ease}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-success-tint);background:var(--ion-color-success-tint);transform:translateY(-2px);box-shadow:0 4px 12px #2dd36f26}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:var(--ion-color-dark);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]{margin:0 0 8px;font-size:13px;color:var(--ion-color-medium);line-height:1.4}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]{display:flex;gap:12px;flex-wrap:wrap}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%], .all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .capacity[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-success);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:18px}.all-centers-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#00000080;z-index:1500;opacity:0;visibility:hidden;transition:all .3s ease}.all-centers-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: #2dd36f;--color: white;--border-width: 0}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;font-size:18px}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-success);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}.route-footer[_ngcontent-%COMP%]{position:fixed;bottom:-100px;left:0;right:0;background:#fff;border-top:1px solid var(--ion-color-light);box-shadow:0 -4px 20px #0000001a;z-index:1500;transition:bottom .3s ease-in-out}.route-footer.show[_ngcontent-%COMP%]{bottom:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:16px 20px;display:flex;align-items:center;justify-content:space-between;gap:16px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;flex:1}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:var(--ion-color-success-tint);display:flex;align-items:center;justify-content:center}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--ion-color-success)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{flex:1}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .destination[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-dark);margin-bottom:2px;line-height:1.2}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]{display:flex;gap:8px;align-items:center}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:var(--ion-color-success)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 20px;--padding-start: 20px;--padding-end: 20px;font-weight:600}.navigation-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-100%;width:320px;height:100vh;background:#fff;z-index:2000;transition:right .3s ease-in-out;box-shadow:-4px 0 20px #00000026}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;padding:60px 20px 20px}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid var(--ion-color-light)}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:18px;font-weight:600;color:var(--ion-color-dark);line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);line-height:1.3}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px;font-weight:600;color:var(--ion-color-success)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .option-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:24px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border:2px solid var(--ion-color-light);border-radius:12px;background:#fff;cursor:pointer;transition:all .2s ease;position:relative}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-success-tint);background:var(--ion-color-success-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]{border-color:var(--ion-color-success);background:var(--ion-color-success-tint)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-success)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-right:12px;color:var(--ion-color-medium);transition:color .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--ion-color-dark);flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:2px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-success)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-btn[_ngcontent-%COMP%]   .route-info[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]{width:100%;padding:16px;background:var(--ion-color-success);color:#fff;border:none;border-radius:12px;font-size:16px;font-weight:600;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:background .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]:hover{background:var(--ion-color-success-shade)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .start-navigation-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}@media (max-width: 768px){.map-controls[_ngcontent-%COMP%]{top:70px;right:8px}.all-centers-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:12px 16px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]{gap:10px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:36px;height:36px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .destination[_ngcontent-%COMP%]{font-size:14px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:13px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:11px}}.travel-mode-selector[_ngcontent-%COMP%]{position:absolute;top:20px;left:20px;z-index:1000;background:#fffffff2;border-radius:12px;padding:8px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{--background: transparent;min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--color-checked: var(--ion-color-success);--indicator-color: var(--ion-color-success);min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:2px}.travel-mode-selector[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-info[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;z-index:1000;max-width:200px}.route-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;background:#fffffff2}.route-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-success);margin-bottom:8px}.route-info[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;color:var(--ion-color-dark)}.route-info[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;color:var(--ion-color-success)}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse-container[_ngcontent-%COMP%]{position:relative;width:50px;height:50px;display:flex;align-items:center;justify-content:center}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse[_ngcontent-%COMP%]{position:absolute;width:40px;height:40px;border-radius:50%;animation:_ngcontent-%COMP%_pulse 2s infinite;opacity:.6}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-icon[_ngcontent-%COMP%]{width:30px;height:30px;z-index:2;position:relative}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-label[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:#2dd36f;color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;z-index:3;border:2px solid white}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.2);opacity:.4}to{transform:scale(.8);opacity:.8}}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-success);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup.nearest-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#2dd36f;font-size:18px}"]})}}return c})();export{Jt as TyphoonMapPage};
