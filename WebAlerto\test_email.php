<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Mail;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    Mail::raw('This is a test email from ALERTO system to verify email configuration.', function ($message) {
        $message->to('<EMAIL>')
                ->subject('ALERTO - Email Configuration Test');
    });
    
    echo "Test email sent successfully!\n";
} catch (Exception $e) {
    echo "Error sending email: " . $e->getMessage() . "\n";
}
