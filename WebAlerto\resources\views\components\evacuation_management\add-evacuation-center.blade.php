@extends('layout.app')

@section('title', 'Add Evacuation Center')

@section('content')
<!-- Add loading overlay -->
<div id="mapLoadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[2000] hidden">
    <div class="bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-xl text-center max-w-sm w-full mx-4">
        <div class="animate-spin rounded-full h-16 w-16 border-4 border-sky-500 border-t-transparent mx-auto mb-4"></div>
        <p class="text-gray-700 text-lg font-medium">Loading Map...</p>
        <p class="text-gray-500 text-sm mt-2">Please wait while we initialize the map</p>
    </div>
</div>

<!-- Add error message container -->
<div id="mapError" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl relative mb-4 z-[1000]" role="alert">
    <strong class="font-bold">Error!</strong>
    <span class="block sm:inline">Failed to load the map. Please check your internet connection and try again.</span>
    <button onclick="retryMapLoad()" class="absolute top-0 bottom-0 right-0 px-4 py-3">
        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <title>Retry</title>
            <path d="M14.66 15.66A8 8 0 1 1 17 10h-2a6 6 0 1 0-1.76 4.24l1.42 1.42zM12 10h8l-4 4-4-4z"/>
        </svg>
    </button>
</div>

<div class="min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-sky-100 py-8">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back Button and Title -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-6 mb-8">
            <div class="flex items-center gap-4">
                <a href="{{ route('components.evacuation_management.evacuation-dashboard') }}" 
                   class="p-2 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white hover:from-sky-600 hover:to-blue-700 transition-all">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                </a>
                <div>
                    <h1 class="text-3xl md:text-4xl font-extrabold text-gray-900">Add New Evacuation Center</h1>
                    <p class="text-gray-600 mt-1">Create a new evacuation center with location details</p>
                </div>
            </div>
        </div>

        @if ($errors->any())
            <div class="mb-6 bg-red-50/80 backdrop-blur-sm border-l-4 border-red-500 p-4 rounded-xl shadow-lg">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <h3 class="text-red-800 font-medium">Please correct the following errors:</h3>
                </div>
                <ul class="list-disc pl-5 space-y-1">
                    @foreach ($errors->all() as $error)
                        <li class="text-red-700">{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Step 1: Basic Information -->
        <div id="step1" class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-8">
            <div class="max-w-3xl mx-auto">
                <div class="flex items-center gap-6 mb-8">
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white text-xl font-semibold w-12 h-12 flex items-center justify-center">1</div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Basic Information</h2>
                        <p class="text-gray-600 mt-1">Enter the details of the evacuation center</p>
                    </div>
                </div>

                <form id="evacuationCenterForm" action="{{ route('components.evacuation_management.store') }}" method="POST">
                    @csrf
                    <div class="grid grid-cols-1 gap-8">
                        <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                            <label for="name" class="block text-xl font-semibold text-sky-600 mb-3">Center Name</label>
                            <input type="text" id="name" name="name" 
                                   class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-lg py-4 px-4 bg-white" 
                                   required value="{{ old('name') }}"
                                   placeholder="Enter center name">
                        </div>
                        <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                            <label for="capacity" class="block text-xl font-semibold text-sky-600 mb-3">Capacity</label>
                            <input type="number" id="capacity" name="capacity" 
                                   class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-lg py-4 px-4 bg-white" 
                                   required value="{{ old('capacity') }}"
                                   placeholder="Enter capacity">
                        </div>
                        <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                            <label for="contact" class="block text-xl font-semibold text-sky-600 mb-3">Contact Number</label>
                            <input type="text" id="contact" name="contact" 
                                   class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-lg py-4 px-4 bg-white" 
                                   required value="{{ old('contact') }}"
                                   placeholder="Enter contact number">
                        </div>
                        
                        <div class="bg-sky-50 p-6 rounded-lg border border-sky-100 disaster-type-container">
                            <label class="block text-xl font-semibold text-sky-600 mb-3">Disaster Type</label>
                            <div class="space-y-2">
                                <div class="flex items-center gap-3">
                                    <input type="checkbox" id="typhoon" name="disaster_type[]" value="Typhoon" class="h-5 w-5 text-green-600 border-gray-300 rounded focus:ring-green-500" {{ is_array(old('disaster_type')) && in_array('Typhoon', old('disaster_type')) ? 'checked' : '' }}>
                                    <label for="typhoon" class="text-lg text-gray-800">Typhoon <span class="inline-block w-4 h-4 rounded-full align-middle ml-1" style="background-color: #22c55e;"></span></label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <input type="checkbox" id="flood" name="disaster_type[]" value="Flood" class="h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500" {{ is_array(old('disaster_type')) && in_array('Flood', old('disaster_type')) ? 'checked' : '' }}>
                                    <label for="flood" class="text-lg text-gray-800">Flood <span class="inline-block w-4 h-4 rounded-full align-middle ml-1" style="background-color: #3b82f6;"></span></label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <input type="checkbox" id="fire" name="disaster_type[]" value="Fire" class="h-5 w-5 text-red-600 border-gray-300 rounded focus:ring-red-500" {{ is_array(old('disaster_type')) && in_array('Fire', old('disaster_type')) ? 'checked' : '' }}>
                                    <label for="fire" class="text-lg text-gray-800">Fire <span class="inline-block w-4 h-4 rounded-full align-middle ml-1" style="background-color: #ef4444;"></span></label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <input type="checkbox" id="earthquake" name="disaster_type[]" value="Earthquake" class="h-5 w-5 text-orange-500 border-gray-300 rounded focus:ring-orange-500" {{ is_array(old('disaster_type')) && in_array('Earthquake', old('disaster_type')) ? 'checked' : '' }}>
                                    <label for="earthquake" class="text-lg text-gray-800">Earthquake <span class="inline-block w-4 h-4 rounded-full align-middle ml-1" style="background-color: #f59e42;"></span></label>
                                </div>
                                <div class="flex items-center gap-3">
                                    <input type="checkbox" id="landslide" name="disaster_type[]" value="Landslide" class="h-5 w-5 text-yellow-700 border-gray-300 rounded focus:ring-yellow-700" {{ is_array(old('disaster_type')) && in_array('Landslide', old('disaster_type')) ? 'checked' : '' }}>
                                    <label for="landslide" class="text-lg text-gray-800">Landslide <span class="inline-block w-4 h-4 rounded-full align-middle ml-1" style="background-color: #a16207;"></span></label>
                                </div>
                            </div>
                            <div id="selectedDisasters" class="flex flex-wrap gap-2 mt-2"></div>
                            <p class="text-sm text-gray-500 mt-2">If more than one disaster is selected, this center will use a <span class="inline-block w-4 h-4 rounded-full align-middle" style="background-color: #6b7280;"></span> gray marker on the map.</p>
                        <p class="text-sm text-gray-500 mt-2">If "Others" is selected alone, this center will use a <span class="inline-block w-4 h-4 rounded-full align-middle" style="background-color: #9333ea;"></span> violet marker on the map.</p>
                    </div>
                    </div>

                    <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                            <label for="status" class="block text-xl font-semibold text-sky-600 mb-3">Status</label>
                            <select id="status" name="status" 
                                    class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-lg py-4 px-4 bg-white" required>
                                <option value="">Select status</option>
                                <option value="Active" {{ old('status') == 'Active' ? 'selected' : '' }}>Active</option>
                                <option value="Inactive" {{ old('status') == 'Inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>

                    <div class="flex justify-end gap-4 pt-6">
                        <a href="{{ route('components.evacuation_management.evacuation-dashboard') }}"
                           class="px-6 py-3 border-2 border-sky-200 text-sky-700 font-medium text-base rounded-xl hover:bg-sky-50 transition-all duration-200">
                            Cancel
                        </a>
                        <button type="button" id="nextStep" 
                                class="px-6 py-3 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white font-medium text-base rounded-xl shadow-lg transition-all duration-200 transform hover:scale-105 flex items-center gap-2">
                            Next Step
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </button>
                    </div>
            </div>
        </div>

        <!-- Step 2: Location Confirmation -->
        <div id="step2" class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-sky-200 p-8 mt-6 {{ $errors->any() && old('latitude') ? '' : 'hidden' }}">
            <div class="max-w-3xl mx-auto">
                <div class="flex items-center gap-6 mb-8">
                    <div class="p-3 bg-gradient-to-br from-sky-500 to-blue-600 rounded-xl shadow-lg text-white text-xl font-semibold w-12 h-12 flex items-center justify-center">2</div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Location Details</h2>
                        <p class="text-gray-600 mt-1">Set and confirm the center's location</p>
                    </div>
                </div>

                <div class="space-y-8">
                    <!-- Search Box -->
                    <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                        <label for="search" class="block text-xl font-semibold text-sky-600 mb-3">Search Location</label>
                        <div class="relative">
                            <input type="text" id="search"
                                   class="w-full rounded-lg border-2 border-gray-300 shadow-sm focus:border-sky-500 focus:ring-sky-500 text-lg py-4 pl-12 pr-4 bg-white"
                                   placeholder="Search for a location..."
                                   value="{{ collect([old('street_name'), old('barangay'), old('city'), old('province')])->filter()->implode(', ') }}">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                            <div id="searchResults" class="absolute z-[1000] w-full bg-white border border-gray-300 rounded-lg mt-2 shadow-lg max-h-60 overflow-y-auto hidden"></div>
                        </div>
                    </div>

                    <!-- Map Container -->
                    <div class="bg-sky-50 p-6 border border-sky-100 relative">
                        <label class="block text-xl font-semibold text-sky-600 mb-3">Map View</label>
                        <div class="overflow-hidden border-2 border-gray-300 relative">
                            <div id="map" class="h-[400px] w-full relative z-[10]">
                                <div id="mapLoadingIndicator" class="absolute inset-0 bg-white flex items-center justify-center z-[15]">
                                    <div class="text-center">
                                        <div class="animate-spin h-12 w-12 border-4 border-sky-500 border-t-transparent"></div>
                                        <p class="text-gray-600 mt-2 text-lg">Loading map...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 space-y-2 text-gray-600">
                            <p class="flex items-center gap-2">
                                <svg class="h-5 w-5 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Click on the map or search for a location to set the center's position
                            </p>
                            <p class="flex items-center gap-2">
                                <svg class="h-5 w-5 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"/>
                                </svg>
                                Drag the marker to adjust the location if needed
                            </p>
                            <p class="flex items-center gap-2">
                                <svg class="h-5 w-5 text-sky-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Confirm the location is correct before saving
                            </p>
                        </div>
                    </div>

                    <!-- Location Details Card -->
                    <div class="bg-sky-50 p-6 rounded-lg border border-sky-100">
                        <label class="block text-xl font-semibold text-gray-900 mb-3">Selected Location Details</label>
                        <div class="space-y-4">
                            <p id="selectedAddress" class="text-lg text-gray-700 bg-white p-4 rounded-lg border-2 border-gray-300">
                                @if(old('street_name') || old('barangay') || old('city') || old('province'))
                                    {{ old('street_name') }}, {{ old('barangay') }}, {{ old('city') }}, {{ old('province') }}
                                @else
                                    No location selected yet
                                @endif
                            </p>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-white p-4 rounded-lg border-2 border-gray-300">
                                    <label class="block text-sm font-medium text-gray-500 mb-1">Latitude</label>
                                    <p id="selectedLatitude" class="text-lg text-gray-700">
                                        @if(old('latitude'))
                                            {{ old('latitude') }}
                                        @else
                                            --
                                        @endif
                                    </p>
                        </div>
                                <div class="bg-white p-4 rounded-lg border-2 border-gray-300">
                                    <label class="block text-sm font-medium text-gray-500 mb-1">Longitude</label>
                                    <p id="selectedLongitude" class="text-lg text-gray-700">
                                        @if(old('longitude'))
                                            {{ old('longitude') }}
                                        @else
                                            --
                                        @endif
                                    </p>
                            </div>
                            </div>
                            <div id="provinceWarning" class="hidden mt-3 bg-amber-50 p-4 rounded-lg flex items-start gap-3 border border-amber-200">
                                <svg class="h-6 w-6 text-amber-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                </svg>
                            <div>
                                    <h4 class="text-amber-800 font-medium">Location Warning</h4>
                                    <p class="text-amber-700">Province could not be determined. Please select a more specific location on the map.</p>
                            </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden form fields for location data -->
                    <input type="hidden" id="latitude" name="latitude" required value="{{ old('latitude') }}">
                    <input type="hidden" id="longitude" name="longitude" required value="{{ old('longitude') }}">
                    <input type="hidden" id="street_name" name="street_name" required value="{{ old('street_name') }}">
                    <input type="hidden" id="province" name="province" required value="{{ old('province') }}">
                    <input type="hidden" id="city" name="city" required value="{{ old('city') }}">
                    <input type="hidden" id="postal_code" name="postal_code" required value="{{ old('postal_code') }}">
                    <input type="hidden" id="barangay" name="barangay" required value="{{ old('barangay') }}">

                    <!-- Action Buttons -->
                    <div class="flex justify-end gap-4 pt-6">
                        <button type="button" id="prevStep"
                                class="px-6 py-3 border-2 border-sky-200 text-sky-700 font-medium text-base rounded-xl hover:bg-sky-50 transition-all duration-200 flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                            <span>Back</span>
                        </button>
                        <button type="submit" id="saveButton"
                                 class="px-6 py-3 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white font-medium text-base rounded-xl shadow-lg transition-all duration-200 transform hover:scale-105 flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            Save Center
                        </button>
                    </div>
                </div>
                </form>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""/>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>

<script>
// Fix the alert override to ensure input fields still work
(function() {
    // Save the original alert function
    const originalAlert = window.alert;
    
    // Override the alert function
    window.alert = function(message) {
        console.log("Alert suppressed:", message);
        
        // Instead of showing an alert, show an inline message
        if (message && message.includes("required fields")) {
            // Do nothing - we already have inline validation
            return;
        }
        
        // For other alerts, show them inline
        const errorContainer = document.createElement('div');
        errorContainer.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl relative mb-4 error-indicator';
        errorContainer.innerHTML = `
            <span class="block sm:inline">${message}</span>
            <button type="button" class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.remove()">
                <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <title>Close</title>
                    <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                </svg>
            </button>
        `;
        
        const formElement = document.getElementById('evacuationCenterForm');
        if (formElement) {
            formElement.prepend(errorContainer);
            errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    };
})();

let map;
let marker;
let searchTimeout;
let mapLoadAttempts = 0;
const MAX_LOAD_ATTEMPTS = 3;
const isAdmin = {{ auth()->user()->hasRole('admin') || auth()->user()->hasRole('super_admin') ? 'true' : 'false' }};
const userBarangay = '{{ auth()->user()->barangay }}';

// Disaster type color legend
const disasterTypeColors = {
    'Typhoon': '#22c55e',
    'Flood': '#3b82f6',
    'Fire': '#ef4444',
    'Earthquake': '#f59e42',
    'Landslide': '#a16207',
    'Others': '#9333ea',    // Set to violet color
    'Custom': '', 
    'Multi-disaster': '#6b7280'
};
const disasterTypeBadgeClasses = {
    'Typhoon': 'bg-green-100 text-green-800',
    'Flood': 'bg-blue-100 text-blue-800',
    'Fire': 'bg-red-100 text-red-800',
    'Earthquake': 'bg-orange-100 text-orange-800',
    'Landslide': 'bg-yellow-100 text-yellow-800',
    'Multi-disaster': 'bg-gray-200 text-gray-800'
};

function getSelectedDisasters() {
    return Array.from(document.querySelectorAll('input[name="disaster_type[]"]:checked')).map(cb => cb.value);
}

function updateSelectedDisastersDisplay() {
    const selected = getSelectedDisasters();
    const container = document.getElementById('selectedDisasters');
    container.innerHTML = '';
    if (selected.length === 0) {
        container.innerHTML = '<span class="text-gray-400 text-sm">No disaster type selected</span>';
    } else {
        selected.forEach(type => {
            const span = document.createElement('span');
            span.className = `px-2 py-1 rounded-full text-xs font-medium ${disasterTypeBadgeClasses[type] || 'bg-gray-200 text-gray-800'}`;
            span.textContent = type;
            container.appendChild(span);
        });
    }
}

// Add event listeners to disaster type checkboxes
document.querySelectorAll('input[name="disaster_type[]"]').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedDisastersDisplay);
});

// On page load, show selected disasters
updateSelectedDisastersDisplay();

// Function to load barangay boundaries
async function loadBarangayBoundaries() {
    try {
        const response = await fetch(`/api/barangays/${userBarangay}`);
        const data = await response.json();
        
        if (data.coordinates) {
            const barangayLayer = L.geoJSON({
                type: 'Feature',
                geometry: {
                    type: 'Polygon',
                    coordinates: data.coordinates.coordinates
                }
            }, {
                style: {
                    color: '#2563eb',
                    weight: 2,
                    opacity: 0.6,
                    fillColor: '#3b82f6',
                    fillOpacity: 0.1
                }
            }).addTo(map);
            
            map.fitBounds(barangayLayer.getBounds());
        }
    } catch (error) {
        console.error('Error loading barangay boundaries:', error);
    }
}

// Function to check if point is within barangay
async function isPointInBarangay(lat, lng) {
    try {
        const response = await fetch(`/api/barangays/${userBarangay}/contains-point?lat=${lat}&lng=${lng}`);
        const data = await response.json();
        return data.success && data.inside;
    } catch (error) {
        console.error('Error checking point location:', error);
        return false;
    }
}

// Completely rewrite the updateMarker function to ensure it works correctly
function updateMarker(lat, lng) {
    // Remove existing marker if it exists
    if (marker) {
        map.removeLayer(marker);
    }

    // Ensure exact precision for coordinates
    lat = parseFloat(parseFloat(lat).toFixed(8));
    lng = parseFloat(parseFloat(lng).toFixed(8));

    // Get selected disaster types
    const checkboxes = document.querySelectorAll('input[name="disaster_type[]"]:checked');
    const selectedTypes = Array.from(checkboxes).map(cb => cb.value);
    
    // Determine marker color based on selection
    let markerColor;
    
    console.log("Selected disaster types:", selectedTypes); // Debug log
    
    if (selectedTypes.length === 0) {
        markerColor = null; // Will be caught by validation
    } else if (selectedTypes.length > 1) {
        markerColor = disasterTypeColors['Multi-disaster']; // Gray for multiple selections
    } else {
        // Single selection - get the specific color
        const selectedType = selectedTypes[0];
        markerColor = disasterTypeColors[selectedType];
        
        console.log("Selected type:", selectedType, "Color:", markerColor); // Debug log
        
        // Fallback if color is undefined
        if (!markerColor) {
            console.error("Color not found for type:", selectedType);
            markerColor = '#6b7280'; // Default to gray
        }
    }
    
    // Force the color for 'Others' to ensure it works
    if (selectedTypes.length === 1 && selectedTypes[0] === 'Others') {
        markerColor = '#9333ea'; // Violet/Purple
        console.log("Forcing violet color for Others type");
    }

    // Create a custom marker with the determined color
    const markerIcon = L.divIcon({
        html: `<div style="background-color: ${markerColor}; width: 24px; height: 24px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.4);"></div>`,
        className: 'custom-div-icon',
        iconSize: [24, 24],
        iconAnchor: [12, 12]
    });

    // Create and add the marker to the map
    marker = L.marker([lat, lng], { 
        draggable: true,
        autoPan: true,
        icon: markerIcon
    }).addTo(map);

    // Set up drag event handler
    marker.on('dragend', function(e) {
        const position = marker.getLatLng();
        const newLat = parseFloat(parseFloat(position.lat).toFixed(8));
        const newLng = parseFloat(parseFloat(position.lng).toFixed(8));
        
        // Center map on new marker position after drag
        map.setView([newLat, newLng], map.getZoom());
        updateMarkerPosition(newLat, newLng);
    });

    // Update form values with exact precision
    document.getElementById('latitude').value = lat;
    document.getElementById('longitude').value = lng;
    document.getElementById('selectedLatitude').textContent = lat.toFixed(8);
    document.getElementById('selectedLongitude').textContent = lng.toFixed(8);
    
    // Update address details
    reverseGeocode(lat, lng);
}

function updateMarkerPosition(lat, lng) {
    // Update form values
    document.getElementById('latitude').value = lat;
    document.getElementById('longitude').value = lng;
    document.getElementById('selectedLatitude').textContent = lat.toFixed(8);
    document.getElementById('selectedLongitude').textContent = lng.toFixed(8);
    
    // Update address details
    reverseGeocode(lat, lng);
}

// Initialize map
function initializeMap() {
    try {
        document.getElementById('mapLoadingIndicator').classList.remove('hidden');
        map = L.map('map').setView([10.3157, 123.8854], 13);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 19,
            subdomains: 'abc'
        }).addTo(map);

        map.on('click', function(e) {
            const lat = parseFloat(e.latlng.lat.toFixed(8));
            const lng = parseFloat(e.latlng.lng.toFixed(8));
            updateMarker(lat, lng);
        });

        // Hide loading indicator after map is loaded
        setTimeout(() => {
            document.getElementById('mapLoadingIndicator').classList.add('hidden');
        }, 1000);
    } catch (error) {
        console.error('Error initializing map:', error);
        document.getElementById('mapError').classList.remove('hidden');
        document.getElementById('mapLoadingIndicator').classList.add('hidden');
    }
}

// Show correct step on page load if validation failed
document.addEventListener('DOMContentLoaded', function() {
    // Ensure save button is always enabled
    const saveButton = document.getElementById('saveButton');
    if (saveButton) {
        saveButton.disabled = false;
    }

    @if ($errors->any() && old('latitude'))
        document.getElementById('step1').classList.add('hidden');
        document.getElementById('step2').classList.remove('hidden');
        if (!map) {
            initializeMap();
        }
        // Repopulate marker if coordinates exist
        @if(old('latitude') && old('longitude'))
            setTimeout(() => {
                updateMarker({{ old('latitude') }}, {{ old('longitude') }});
            }, 800);
        @endif
    @else
        document.getElementById('step1').classList.remove('hidden');
        document.getElementById('step2').classList.add('hidden');
    @endif
});

// Function to retry map loading
function retryMapLoad() {
    if (mapLoadAttempts < MAX_LOAD_ATTEMPTS) {
        mapLoadAttempts++;
        document.getElementById('mapError').classList.add('hidden');
        document.getElementById('mapLoadingOverlay').classList.remove('hidden');
        initializeMap();
    } else {
        alert('Failed to load map after multiple attempts. Please refresh the page or try again later.');
    }
}

// Fix the nextStep event listener to ensure proper validation and navigation
document.getElementById('nextStep').addEventListener('click', function() {
    // Clear previous error indicators
    document.querySelectorAll('.error-indicator').forEach(el => el.remove());
    document.querySelectorAll('.border-red-500').forEach(el => {
        el.classList.remove('border-red-500');
        el.classList.add('border-sky-100');
    });

    // Define required fields for step 1
    const requiredFields = [
        { id: 'name', label: 'Center Name' },
        { id: 'capacity', label: 'Capacity' },
        { id: 'contact', label: 'Contact Number' },
        { id: 'status', label: 'Status' }
    ];
    
    let hasErrors = false;
    
    // Check each required field
    requiredFields.forEach(field => {
        const element = document.getElementById(field.id);
        if (!element || !element.value.trim()) {
            hasErrors = true;
            
            // Highlight the field
            element.classList.remove('border-gray-300');
            element.classList.add('border-red-500');
            
            // Add error message below the field
            const errorMsg = document.createElement('p');
            errorMsg.className = 'text-red-500 text-sm mt-1 error-indicator';
            errorMsg.textContent = `${field.label} is required`;
            element.parentNode.appendChild(errorMsg);
        }
    });
    
    // Check if at least one disaster type is selected
    const disasterTypesSelected = document.querySelectorAll('input[name="disaster_type[]"]:checked').length > 0;
    if (!disasterTypesSelected) {
        hasErrors = true;
        
        // Add error message to disaster type section
        const disasterTypeContainer = document.querySelector('.disaster-type-container');
        
        // Add red border to the container
        disasterTypeContainer.classList.remove('border-sky-100');
        disasterTypeContainer.classList.add('border-red-500');
        
        // Add error message
        const errorMsg = document.createElement('p');
        errorMsg.className = 'text-red-500 text-sm mt-2 error-indicator';
        errorMsg.textContent = 'Please select at least one disaster type';
        disasterTypeContainer.appendChild(errorMsg);
    }
    
    if (hasErrors) {
        // Scroll to the first error
        const firstError = document.querySelector('.border-red-500') || 
                          document.querySelector('.error-indicator');
        if (firstError) {
            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        return;
    }
    
    // If no errors, proceed to step 2
    document.getElementById('mapLoadingOverlay').classList.remove('hidden');
    
    setTimeout(() => {
        document.getElementById('step1').classList.add('hidden');
        document.getElementById('step2').classList.remove('hidden');
    
        // Initialize map if not already initialized
        if (!map) {
            initializeMap();
            mapInitialized = true;
        } else {
            map.invalidateSize();
            
            // Restore marker if we have saved position
            if (savedMarkerPosition) {
                console.log("Restoring marker at:", savedMarkerPosition);
                setTimeout(() => {
                    updateMarker(savedMarkerPosition.lat, savedMarkerPosition.lng);
                }, 300);
            }
        }

        // Scroll to top of the page
        window.scrollTo({ top: 0, behavior: 'smooth' });
    
        // Hide loading overlay after map is shown
        setTimeout(() => {
            document.getElementById('mapLoadingOverlay').classList.add('hidden');
        }, 500);
    }, 100);
});

// Add event listeners to disaster type checkboxes to clear error state
document.querySelectorAll('input[name="disaster_type[]"]').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const container = this.closest('.bg-sky-50');
        if (document.querySelectorAll('input[name="disaster_type[]"]:checked').length > 0) {
            container.classList.remove('border-red-500');
            container.classList.add('border-sky-100');
            const errorMsg = container.querySelector('.error-indicator');
            if (errorMsg) {
                errorMsg.remove();
            }
        }
    });
});

// Improve map and marker persistence between steps
let mapInitialized = false;
let savedMarkerPosition = null;

// Modify the prevStep button event listener
document.getElementById('prevStep').addEventListener('click', function() {
    document.getElementById('step2').classList.add('hidden');
    document.getElementById('step1').classList.remove('hidden');
    
    // Instead of removing the map, just hide it and save marker position
    if (map && marker) {
        savedMarkerPosition = marker.getLatLng();
        console.log("Saved marker position:", savedMarkerPosition);
    }
    
    // Don't destroy the map, just hide its container
    // if (map) {
    //     map.remove();
    //     map = null;
    // }
});

// Modify the nextStep button event listener to restore marker
document.getElementById('nextStep').addEventListener('click', function() {
    // Validate form fields
    const name = document.getElementById('name').value;
    const capacity = document.getElementById('capacity').value;
    const contact = document.getElementById('contact').value;
    const disasterTypes = getSelectedDisasters();
    const status = document.getElementById('status').value;
    
    // Check if required fields are filled
    if (!name || !capacity || !contact || disasterTypes.length === 0 || !status) {
        alert('Please fill in all required fields');
        return;
    }
    
    // If no errors, proceed to step 2
    document.getElementById('mapLoadingOverlay').classList.remove('hidden');
    
    setTimeout(() => {
        document.getElementById('step1').classList.add('hidden');
        document.getElementById('step2').classList.remove('hidden');
    
        // Initialize map if not already initialized
        if (!map) {
            initializeMap();
            mapInitialized = true;
        } else {
            map.invalidateSize();
            
            // Restore marker if we have saved position
            if (savedMarkerPosition) {
                console.log("Restoring marker at:", savedMarkerPosition);
                setTimeout(() => {
                    updateMarker(savedMarkerPosition.lat, savedMarkerPosition.lng);
                }, 300);
            }
        }

        // Scroll to top of the page
        window.scrollTo({ top: 0, behavior: 'smooth' });
    
        // Hide loading overlay after map is shown
        setTimeout(() => {
            document.getElementById('mapLoadingOverlay').classList.add('hidden');
        }, 500);
    }, 100);
});

// Modify the updateMarker function to save position
const originalUpdateMarker = updateMarker;
window.updateMarker = function(lat, lng) {
    // Call the original function
    originalUpdateMarker(lat, lng);
    
    // Save the marker position
    savedMarkerPosition = { lat, lng };
    console.log("Updated and saved marker position:", savedMarkerPosition);
};

// Initialize Nominatim geocoder
const searchInput = document.getElementById('search');
const searchResults = document.getElementById('searchResults');
const selectedAddress = document.getElementById('selectedAddress');
const selectedCoordinates = document.getElementById('selectedCoordinates');
const saveButton = document.getElementById('saveButton');
const provinceWarning = document.getElementById('provinceWarning');

// Handle search input
searchInput.addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const query = this.value.trim();
    if (query.length < 3) {
        searchResults.classList.add('hidden');
        return;
    }
    searchTimeout = setTimeout(() => {
        searchResults.innerHTML = '<div class="p-2 text-gray-500">Searching...</div>';
        searchResults.classList.remove('hidden');
        geocodeAddress(query);
    }, 500);
});

function geocodeAddress(address) {
    const params = new URLSearchParams({
        format: 'json',
        q: address,
        limit: '5',
        addressdetails: '1',
        countrycodes: 'ph'
    });

    fetch(`https://nominatim.openstreetmap.org/search?${params.toString()}`, {
        headers: {
            'Accept': 'application/json',
            'User-Agent': 'WebAlerto/1.0'
        }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            searchResults.innerHTML = '';
            if (data.length > 0) {
            for (const result of data) {
                    const div = document.createElement('div');
                div.className = 'p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0';
                    div.textContent = result.display_name;
                
                    div.addEventListener('click', () => {
                        const lat = parseFloat(result.lat);
                        const lon = parseFloat(result.lon);

                    // Center map on selected location with appropriate zoom
                    map.setView([lat, lon], 16);
                    
                    // Add marker after a short delay to ensure map is centered
                    setTimeout(() => {
                        updateMarker(lat, lon);
                    }, 100);

                        searchInput.value = result.display_name;
                        searchResults.classList.add('hidden');
                });
                
                searchResults.appendChild(div);
            }
            } else {
                searchResults.innerHTML = '<div class="p-2 text-gray-500">No results found</div>';
        }
                searchResults.classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error during geocoding:', error);
            searchResults.innerHTML = '<div class="p-2 text-red-500">Search failed. Please try again.</div>';
            searchResults.classList.remove('hidden');
        });
}

// Close search results when clicking outside
document.addEventListener('click', function(e) {
    if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
        searchResults.classList.add('hidden');
    }
});

function reverseGeocode(lat, lng) {
    const params = new URLSearchParams({
        format: 'json',
        lat: lat,
        lon: lng,
        addressdetails: '1',
        zoom: '18'
    });

    fetch(`https://nominatim.openstreetmap.org/reverse?${params.toString()}`, {
        headers: {
            'Accept': 'application/json',
            'User-Agent': 'WebAlerto/1.0'
        }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            const address = data.address || {};
            
            // Get the most detailed street name possible
            const street = address.road ||
                          address.pedestrian ||
                          address.footway ||
                          address.street ||
                          address.path ||
                          address.house_number ||
                          address.building ||
                          data.display_name?.split(',')[0] || 'Unknown Street';
                          
            document.getElementById('street_name').value = street;

            // Get the most accurate province name
            const province = address.state || 
                           address.province || 
                           address.region || 
                       address.state_district || 
                       'Cebu'; // Default to Cebu if not found
            document.getElementById('province').value = province;

            // Get the most accurate city name
            let city = address.city || 
                      address.town || 
                  address.municipality || 
                  'Cebu City'; // Default to Cebu City if not found
            document.getElementById('city').value = city;

            // Get postal code
        document.getElementById('postal_code').value = address.postcode || '6000'; // Default Cebu City postal code

            // Get the most accurate barangay name
            const barangay = address.suburb || 
                           address.neighbourhood || 
                           address.quarter || 
                           address.hamlet || 
                           address.village || 
                       address.city_district || 
                       userBarangay; // Use the user's barangay as fallback
            document.getElementById('barangay').value = barangay;

            // Update the display address with the full formatted address
        selectedAddress.textContent = data.display_name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`;

        // Update form values with exact precision
        document.getElementById('latitude').value = lat;
        document.getElementById('longitude').value = lng;

            // Province warning logic (but don't disable save button)
            if (!province) {
                provinceWarning.classList.remove('hidden');
            } else {
                provinceWarning.classList.add('hidden');
            }

            // Always keep save button enabled - validation will happen on submit
            saveButton.disabled = false;

        // Log the values for debugging
        console.log('Location details:', {
            street_name: document.getElementById('street_name').value,
            province: document.getElementById('province').value,
            city: document.getElementById('city').value,
            postal_code: document.getElementById('postal_code').value,
            barangay: document.getElementById('barangay').value,
            latitude: document.getElementById('latitude').value,
            longitude: document.getElementById('longitude').value
        });
        })
        .catch(error => {
            console.error('Error during reverse geocoding:', error);
        selectedAddress.textContent = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
        
        // Set default values for Cebu City
        document.getElementById('street_name').value = 'Unknown Street';
        document.getElementById('province').value = 'Cebu';
        document.getElementById('city').value = 'Cebu City';
        document.getElementById('postal_code').value = '6000';
        document.getElementById('barangay').value = userBarangay;
        document.getElementById('latitude').value = lat;
        document.getElementById('longitude').value = lng;
        
            provinceWarning.classList.add('hidden');

        // Always keep save button enabled
        if (saveButton) {
            saveButton.disabled = false;
        }
        });
}

// Replace the finalForm event listener with evacuationCenterForm
document.getElementById('evacuationCenterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Get form data
    const formData = {
        name: document.getElementById('name').value,
        capacity: document.getElementById('capacity').value,
        contact: document.getElementById('contact').value,
        status: document.getElementById('status').value,
        latitude: document.getElementById('latitude').value,
        longitude: document.getElementById('longitude').value,
        street_name: document.getElementById('street_name').value,
        province: document.getElementById('province').value,
        city: document.getElementById('city').value,
        postal_code: document.getElementById('postal_code').value,
        barangay: document.getElementById('barangay').value,
        disaster_type: getSelectedDisasters()
    };

    console.log('Form data before submission:', formData);

    // Replace alert-based validation with inline validation
    const requiredFields = {
        'name': 'Center Name',
        'capacity': 'Capacity',
        'contact': 'Contact Number',
        'status': 'Status',
        'latitude': 'Latitude',
        'longitude': 'Longitude',
        'street_name': 'Street Name',
        'province': 'Province',
        'city': 'City',
        'postal_code': 'Postal Code',
        'barangay': 'Barangay'
    };

    let hasErrors = false;
    // Clear previous error indicators
    document.querySelectorAll('.error-indicator').forEach(el => el.remove());
    document.querySelectorAll('.border-red-500').forEach(el => {
        el.classList.remove('border-red-500');
        el.classList.add('border-sky-100');
    });

    // Check each required field
    for (const [field, label] of Object.entries(requiredFields)) {
        const element = document.getElementById(field);
        const value = formData[field];
        if (!value || value.trim() === '') {
            hasErrors = true;
            
            // Highlight the field if it exists in the current step
            if (element) {
                element.classList.remove('border-gray-300');
                element.classList.add('border-red-500');
                
                // Add error message below the field
                const errorMsg = document.createElement('p');
                errorMsg.className = 'text-red-500 text-sm mt-1 error-indicator';
                errorMsg.textContent = `${label} is required`;
                element.parentNode.appendChild(errorMsg);
            }
        }
    }

    // Check disaster types
    if (formData.disaster_type.length === 0) {
        hasErrors = true;
        const disasterTypeContainer = document.querySelector('.disaster-type-container');
        if (disasterTypeContainer) {
            disasterTypeContainer.classList.remove('border-sky-100');
            disasterTypeContainer.classList.add('border-red-500');
            
            const errorMsg = document.createElement('p');
            errorMsg.className = 'text-red-500 text-sm mt-2 error-indicator';
            errorMsg.textContent = 'Please select at least one disaster type';
            disasterTypeContainer.appendChild(errorMsg);
        }
    }

    if (hasErrors) {
        // Scroll to the first error
        const firstError = document.querySelector('.border-red-500') ||
                          document.querySelector('.error-indicator');
        if (firstError) {
            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        return false;
    }

    // If we get here, all validations passed
    console.log("All validations passed, submitting form...");
    
    // Create FormData object
    const submitData = new FormData(this);
    
    // Submit the form
    fetch(this.action, {
        method: 'POST',
        body: submitData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = data.redirect || '{{ route('components.evacuation_management.evacuation-dashboard') }}';
        } else {
            if (data.errors) {
                // Replace alert with inline error display
                for (const [field, errors] of Object.entries(data.errors)) {
                    const element = document.getElementById(field);
                    if (element) {
                        element.classList.add('border-red-500');
                        const errorMsg = document.createElement('p');
                        errorMsg.className = 'text-red-500 text-sm mt-1 error-indicator';
                        errorMsg.textContent = errors.join(', ');
                        element.parentNode.appendChild(errorMsg);
                    }
                }
                
                // Scroll to the first error
                const firstError = document.querySelector('.border-red-500');
                if (firstError) {
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            } else {
                // Show error message in a more user-friendly way
                const errorContainer = document.createElement('div');
                errorContainer.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl relative mb-4';
                errorContainer.innerHTML = `
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline">${data.message || 'An error occurred while saving the evacuation center.'}</span>
                `;
                
                const formElement = document.getElementById('evacuationCenterForm');
                formElement.prepend(errorContainer);
                
                // Scroll to the error message
                errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // Replace alert with inline error display
        const errorContainer = document.createElement('div');
        errorContainer.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl relative mb-4';
        errorContainer.innerHTML = `
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">An error occurred while saving. Please try again.</span>
        `;
        
        const formElement = document.getElementById('evacuationCenterForm');
        formElement.prepend(errorContainer);
        
        // Scroll to the error message
        errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
    
    return false;
});

// Add the Others checkbox and input field without breaking existing functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add this after the existing disaster type checkboxes, before the closing </div>
    const disasterTypeContainer = document.querySelector('.disaster-type-container .space-y-2');
    
    if (disasterTypeContainer) {
        // Create the Others checkbox
        const othersDiv = document.createElement('div');
        othersDiv.className = 'flex items-center gap-3';
        othersDiv.innerHTML = `
            <input type="checkbox" id="others" name="disaster_type[]" value="Others" class="h-5 w-5 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
            <label for="others" class="text-lg text-gray-800">Others <span class="inline-block w-4 h-4 rounded-full align-middle ml-1" style="background-color: #9333ea;"></span></label>
        `;
        
        // Create the Others input container
        const othersContainer = document.createElement('div');
        othersContainer.id = 'othersContainer';
        othersContainer.className = 'ml-8 mt-1 hidden';
        othersContainer.innerHTML = `
            <input type="text" id="other_disaster_type" name="other_disaster_type" placeholder="Please specify disaster type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
        `;
        
        // Append the new elements
        disasterTypeContainer.appendChild(othersDiv);
        disasterTypeContainer.parentNode.insertBefore(othersContainer, disasterTypeContainer.nextSibling);
        
        // Add event listener for the Others checkbox
        document.getElementById('others').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('othersContainer').classList.remove('hidden');
            } else {
                document.getElementById('othersContainer').classList.add('hidden');
                document.getElementById('other_disaster_type').value = '';
            }
            updateSelectedDisastersDisplay();
        });
        
        // Add event listener for the Others input
        document.getElementById('other_disaster_type').addEventListener('input', updateSelectedDisastersDisplay);
    }
    
    // Update the disasterTypeColors and disasterTypeBadgeClasses objects
    if (typeof disasterTypeColors !== 'undefined') {
        disasterTypeColors['Others'] = '#9333ea'; // Purple color for Others
    }
    
    if (typeof disasterTypeBadgeClasses !== 'undefined') {
        disasterTypeBadgeClasses['Others'] = 'bg-purple-100 text-purple-800';
    }
    
    // Add a custom function to handle Others in the selected disasters display
    const originalUpdateSelectedDisastersDisplay = window.updateSelectedDisastersDisplay;
    if (typeof originalUpdateSelectedDisastersDisplay === 'function') {
        window.updateSelectedDisastersDisplay = function() {
            const selected = getSelectedDisasters();
            const container = document.getElementById('selectedDisasters');
            container.innerHTML = '';
            
            if (selected.length === 0) {
                container.innerHTML = '<span class="text-gray-400 text-sm">No disaster type selected</span>';
            } else {
                selected.forEach(type => {
                    const span = document.createElement('span');
                    
                    if (type.startsWith('Others:')) {
                        // Use violet for Others
                        span.className = 'px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800';
                    } else {
                        span.className = `px-2 py-1 rounded-full text-xs font-medium ${disasterTypeBadgeClasses[type] || 'bg-gray-200 text-gray-800'}`;
                    }
                    
                    span.textContent = type;
                    container.appendChild(span);
                });
            }
        };
    }
});

// Update the getSelectedDisasters function to include Others
const originalGetSelectedDisasters = getSelectedDisasters;
window.getSelectedDisasters = function() {
    const disasters = originalGetSelectedDisasters();
    
    // Check if Others is selected and has a value
    const othersCheckbox = document.getElementById('others');
    const otherDisasterType = document.getElementById('other_disaster_type');
    
    if (othersCheckbox && othersCheckbox.checked && otherDisasterType && otherDisasterType.value.trim()) {
        // Find and remove the plain "Others" value if it exists
        const othersIndex = disasters.indexOf('Others');
        if (othersIndex !== -1) {
            disasters.splice(othersIndex, 1);
        }
        
        // Add the specific Others value
        disasters.push(`Others: ${otherDisasterType.value.trim()}`);
    }
    
    return disasters;
};


// Add event listeners to input fields to clear error state when user starts typing
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all input fields
    const inputFields = ['name', 'capacity', 'contact', 'status'];
    
    inputFields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element) {
            element.addEventListener('input', function() {
                // Remove red border if value is not empty
                if (this.value.trim() !== '') {
                    this.classList.remove('border-red-500');
                    this.classList.add('border-sky-100');
                    
                    // Remove error message
                    const errorMsg = this.parentNode.querySelector('.error-indicator');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                }
            });
        }
    });
    
    // Add event listener to status dropdown
    const statusElement = document.getElementById('status');
    if (statusElement) {
        statusElement.addEventListener('change', function() {
            if (this.value !== '') {
                this.classList.remove('border-red-500');
                this.classList.add('border-sky-100');
                
                // Remove error message
                const errorMsg = this.parentNode.querySelector('.error-indicator');
                if (errorMsg) {
                    errorMsg.remove();
                }
            }
        });
    }
    
    // Add event listeners to disaster type checkboxes
    document.querySelectorAll('input[name="disaster_type[]"]').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const container = this.closest('.bg-sky-50');
            if (document.querySelectorAll('input[name="disaster_type[]"]:checked').length > 0) {
                container.classList.remove('border-red-500');
                container.classList.add('border-sky-100');
                const errorMsg = container.querySelector('.error-indicator');
                if (errorMsg) {
                    errorMsg.remove();
                }
            }
        });
    });
});

// Add event listeners to address fields in step 2
document.addEventListener('DOMContentLoaded', function() {
    const addressFields = ['street_name', 'barangay', 'city', 'province', 'postal_code'];
    
    addressFields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element) {
            element.addEventListener('input', function() {
                // Remove red border if value is not empty
                if (this.value.trim() !== '') {
                    this.classList.remove('border-red-500');
                    this.classList.add('border-sky-100');
                    
                    // Remove error message
                    const errorMsg = this.parentNode.querySelector('.error-indicator');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                }
            });
        }
    });
});

// Check form attributes
console.log("Form method:", document.getElementById('evacuationCenterForm').method);
console.log("Form enctype:", document.getElementById('evacuationCenterForm').enctype);

</script>
@endsection





















