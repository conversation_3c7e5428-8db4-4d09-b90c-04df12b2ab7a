import{a as mt}from"./chunk-OEBCGVFF.js";import{a as ut,c as ft}from"./chunk-YZWAW4SM.js";import{a as _t}from"./chunk-QZ5PEPJK.js";import{b as pt}from"./chunk-I4SN7ED3.js";import{a as dt}from"./chunk-LSM7X32V.js";import"./chunk-3J7GGTVR.js";import{a as gt}from"./chunk-WPPT3EJF.js";import"./chunk-2LL5MXLB.js";import{$ as L,B as c,Eb as Z,F as P,G as d,I as O,Ib as tt,J as o,Jb as et,K as a,L as g,Mb as nt,O as F,P as _,Q as u,Sb as ot,Tb as it,X as l,Y as y,Z as x,Zb as at,_ as T,aa as A,ba as z,bc as rt,ca as D,cb as G,d as S,dc as ct,fb as K,fc as lt,gc as st,ib as J,l as v,m as R,ma as $,na as V,pa as W,r as w,ra as U,s as b,sb as Y,wa as B,wb as q,xb as H,ya as j,yb as Q,zb as X}from"./chunk-SFXIJNIZ.js";import"./chunk-LR6AIEJQ.js";import"./chunk-6NVMNNPA.js";import"./chunk-KW2BML7M.js";import"./chunk-SV2ZKNWA.js";import"./chunk-YJDO75HI.js";import"./chunk-HC6MZPB3.js";import"./chunk-Q5Q6EGIP.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-ZJ5IMUT4.js";import"./chunk-SGSBBWFA.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-BAKMWPBW.js";import"./chunk-EI2QJP5N.js";import"./chunk-W6U2AR23.js";import"./chunk-APL3YEA6.js";import"./chunk-HSXX7Y3C.js";import"./chunk-FUGLTCJS.js";import"./chunk-XTVTS2NW.js";import"./chunk-NMYJD6OP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-SV7S5NYR.js";import{a as N,b as E,f as ht,g as M}from"./chunk-2R6CW7ES.js";var p=ht(ft());function Ct(r,h){if(r&1&&(o(0,"div",45)(1,"ion-card")(2,"ion-card-content")(3,"div",46),g(4,"ion-icon",47),o(5,"span"),l(6,"Route to Nearest Center"),a()(),o(7,"div",39)(8,"div",48),g(9,"ion-icon",38),o(10,"span"),l(11),a()(),o(12,"div",48),g(13,"ion-icon",49),o(14,"span"),l(15),a()()()()()()),r&2){let t=u();c(9),d("name",t.travelMode==="walking"?"walk-outline":t.travelMode==="cycling"?"bicycle-outline":"car-outline"),c(2),x("",(t.routeTime/60).toFixed(0)," min"),c(4),x("",(t.routeDistance/1e3).toFixed(2)," km")}}function Mt(r,h){if(r&1&&(o(0,"span",58),l(1),a()),r&2){let t=u().$implicit,n=u();c(),x(" \u{1F4CD} ",n.calculateDistanceInKm(t)," km away ")}}function Pt(r,h){if(r&1){let t=F();o(0,"div",50),_("click",function(){let e=w(t).$implicit,i=u();return b(i.selectCenterFromList(e))}),o(1,"div",51)(2,"h4"),l(3),a(),o(4,"p",52),l(5),a(),o(6,"div",53),P(7,Mt,2,1,"span",54),o(8,"span",55),l(9),a()()(),o(10,"div",56),g(11,"ion-icon",57),a()()}if(r&2){let t=h.$implicit,n=u();c(3),y(t.name),c(2),y(t.address),c(2),d("ngIf",n.userLocation),c(2),x("\u{1F465} ",t.capacity||"N/A"," capacity")}}function vt(r,h){if(r&1){let t=F();o(0,"app-real-time-navigation",59),_("routeUpdated",function(e){w(t);let i=u();return b(i.onNavigationRouteUpdated(e))})("navigationStopped",function(){w(t);let e=u();return b(e.onNavigationStopped())}),a()}if(r&2){let t=u();d("destination",t.navigationDestination)("travelMode",t.selectedTransportMode==="walking"?"foot-walking":t.selectedTransportMode==="cycling"?"cycling-regular":"driving-car")("autoStart",t.isRealTimeNavigationActive)}}function xt(r,h){if(r&1&&(o(0,"p"),l(1),a()),r&2){let t=u();c(),y(t.selectedCenter.address)}}function Ot(r,h){if(r&1&&(o(0,"div",60),g(1,"ion-icon",63),o(2,"span"),l(3),a()()),r&2){let t=u(2);c(3),x("",t.calculateDistanceInKm(t.selectedCenter)," km away")}}function wt(r,h){if(r&1&&(o(0,"div",53)(1,"div",60),g(2,"ion-icon",49),o(3,"span"),l(4),a()(),o(5,"div",60),g(6,"ion-icon",61),o(7,"span"),l(8),a()(),P(9,Ot,4,1,"div",62),a()),r&2){let t=u();c(4),y(t.selectedCenter.address),c(4),x("Capacity: ",t.selectedCenter.capacity||"N/A",""),c(),d("ngIf",t.userLocation)}}function bt(r,h){if(r&1&&(o(0,"span",70),l(1),a()),r&2){let t=u(2);c(),T(" ",(t.routeInfo.walking.distance/1e3).toFixed(1),"km \u2022 ",t.formatTime(t.routeInfo.walking.duration)," ")}}function yt(r,h){r&1&&(o(0,"span",70),l(1,"Calculating..."),a())}function kt(r,h){if(r&1&&(o(0,"span",70),l(1),a()),r&2){let t=u(2);c(),T(" ",(t.routeInfo.cycling.distance/1e3).toFixed(1),"km \u2022 ",t.formatTime(t.routeInfo.cycling.duration)," ")}}function It(r,h){r&1&&(o(0,"span",70),l(1,"Calculating..."),a())}function Ft(r,h){if(r&1&&(o(0,"span",70),l(1),a()),r&2){let t=u(2);c(),T(" ",(t.routeInfo.driving.distance/1e3).toFixed(1),"km \u2022 ",t.formatTime(t.routeInfo.driving.duration)," ")}}function Tt(r,h){r&1&&(o(0,"span",70),l(1,"Calculating..."),a())}function Nt(r,h){if(r&1){let t=F();o(0,"div",64)(1,"h4"),l(2,"Choose Transport Mode:"),a(),o(3,"div",65),_("click",function(){w(t);let e=u();return b(e.navigateWithMode("walking"))}),o(4,"div",37),g(5,"ion-icon",15),a(),o(6,"div",66)(7,"span",67),l(8,"Walking"),a(),P(9,bt,2,2,"span",68)(10,yt,2,0,"span",68),a(),o(11,"div",69),g(12,"ion-icon",57),a()(),o(13,"div",65),_("click",function(){w(t);let e=u();return b(e.navigateWithMode("cycling"))}),o(14,"div",37),g(15,"ion-icon",17),a(),o(16,"div",66)(17,"span",67),l(18,"Cycling"),a(),P(19,kt,2,2,"span",68)(20,It,2,0,"span",68),a(),o(21,"div",69),g(22,"ion-icon",57),a()(),o(23,"div",65),_("click",function(){w(t);let e=u();return b(e.navigateWithMode("driving"))}),o(24,"div",37),g(25,"ion-icon",19),a(),o(26,"div",66)(27,"span",67),l(28,"Driving"),a(),P(29,Ft,2,2,"span",68)(30,Tt,2,0,"span",68),a(),o(31,"div",69),g(32,"ion-icon",57),a()()()}if(r&2){let t=u();c(3),O("selected",t.selectedTransportMode==="walking"),c(6),d("ngIf",t.routeInfo.walking),c(),d("ngIf",!t.routeInfo.walking),c(3),O("selected",t.selectedTransportMode==="cycling"),c(6),d("ngIf",t.routeInfo.cycling),c(),d("ngIf",!t.routeInfo.cycling),c(3),O("selected",t.selectedTransportMode==="driving"),c(6),d("ngIf",t.routeInfo.driving),c(),d("ngIf",!t.routeInfo.driving)}}function Et(r,h){if(r&1&&(o(0,"div",71)(1,"span",72),l(2),a(),o(3,"span",58),l(4),a()()),r&2){let t=u();c(2),y(t.formatTime(t.routeInfo[t.selectedTransportMode||"walking"]==null?null:t.routeInfo[t.selectedTransportMode||"walking"].duration)),c(2),x("",((t.routeInfo[t.selectedTransportMode||"walking"]==null?null:t.routeInfo[t.selectedTransportMode||"walking"].distance)/1e3).toFixed(1)," km")}}var Xt=(()=>{class r{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.userLocation=null,this.newCenterId=null,this.highlightCenter=!1,this.centerLat=null,this.centerLng=null,this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={},this.showAllCentersPanel=!1,this.showRouteFooter=!1,this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.loadingCtrl=v(ct),this.toastCtrl=v(lt),this.alertCtrl=v(rt),this.http=v(U),this.router=v(j),this.route=v(B),this.osmRouting=v(dt),this.mapboxRouting=v(ut),this.enhancedDownload=v(mt)}ngOnInit(){console.log("\u{1F525} FIRE MAP: Component initialized..."),this.route.queryParams.subscribe(t=>{t.newCenterId&&(this.newCenterId=t.newCenterId,this.highlightCenter=t.highlightCenter==="true",this.centerLat=t.centerLat?parseFloat(t.centerLat):null,this.centerLng=t.centerLng?parseFloat(t.centerLng):null,console.log("\u{1F525} FIRE MAP: New center to highlight:",this.newCenterId))})}ngAfterViewInit(){return M(this,null,function*(){console.log("\u{1F525} FIRE MAP: View initialized, loading map..."),setTimeout(()=>M(this,null,function*(){yield this.loadFireMap()}),100)})}loadFireMap(){return M(this,null,function*(){let t=yield this.loadingCtrl.create({message:"Loading fire evacuation centers...",spinner:"crescent"});yield t.present();try{let n=yield pt.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),e=n.coords.latitude,i=n.coords.longitude;this.userLocation={lat:e,lng:i},console.log(`\u{1F525} FIRE MAP: User location [${e}, ${i}]`),this.initializeMap(e,i),yield this.loadFireCenters(e,i),yield t.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F525} Showing ${this.evacuationCenters.length} fire evacuation centers`,duration:3e3,color:"danger",position:"top"})).present()}catch(n){yield t.dismiss(),console.error("\u{1F525} FIRE MAP: Error loading map",n),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadFireMap()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(t,n){if(console.log(`\u{1F525} FIRE MAP: Initializing map at [${t}, ${n}]`),!document.getElementById("fire-map"))throw console.error("\u{1F525} FIRE MAP: Container #fire-map not found!"),new Error("Map container not found. Please ensure the view is properly loaded.");this.map&&this.map.remove(),this.map=p.map("fire-map").setView([t,n],13),p.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=p.marker([t,n],{icon:p.icon({iconUrl:"assets/Location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadFireCenters(t,n){return M(this,null,function*(){try{console.log("\u{1F525} FIRE MAP: Fetching fire centers...");let e=[];try{e=(yield S(this.http.get(`${gt.apiUrl}/evacuation-centers`))).data||[],console.log("\u{1F525} FIRE MAP: Total centers received from API:",e?.length||0)}catch(i){console.error("\u274C API failed:",i),yield(yield this.alertCtrl.create({header:"Connection Error",message:"Cannot connect to server. Please check your internet connection.",buttons:["OK"]})).present();return}if(this.evacuationCenters=e.filter(i=>Array.isArray(i.disaster_type)?i.disaster_type.some(s=>s==="Fire"):i.disaster_type==="Fire"),console.log(`\u{1F525} FIRE MAP: Filtered to ${this.evacuationCenters.length} fire centers`),console.log("\u{1F525} FIRE MAP: Filtered centers:",this.evacuationCenters.map(i=>`${i.name} (${JSON.stringify(i.disaster_type)})`)),console.log(`\u{1F525} FIRE MAP: Filtered to ${this.evacuationCenters.length} fire centers`),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Fire Centers",message:"No fire evacuation centers found in the data.",buttons:["OK"]})).present();return}yield this.addMarkersAndRoutes(t,n)}catch(e){console.error("\u{1F525} FIRE MAP: Error loading centers",e),yield(yield this.toastCtrl.create({message:"Error loading fire centers. Please check your internet connection.",duration:4e3,color:"danger"})).present()}})}addMarkersAndRoutes(t,n){return M(this,null,function*(){if(this.evacuationCenters.forEach(e=>{let i=Number(e.latitude),s=Number(e.longitude);if(!isNaN(i)&&!isNaN(s)){let m=p.marker([i,s],{icon:p.icon({iconUrl:"assets/forFire.png",iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),C=this.calculateDistance(t,n,i,s);m.on("click",()=>{this.showNavigationPanel(e)});let f=this.newCenterId&&e.id.toString()===this.newCenterId;m.bindPopup(`
          <div class="evacuation-popup">
            <h3>\u{1F525} ${e.name} ${f?"\u2B50 NEW!":""}</h3>
            <p><strong>Type:</strong> Fire Center</p>
            <p><strong>Distance:</strong> ${(C/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${e.capacity||"N/A"}</p>
            <p><em>Click marker for route options</em></p>
            ${f?"<p><strong>\u{1F195} Recently Added!</strong></p>":""}
          </div>
        `),f&&(m.openPopup(),this.map.setView([i,s],15),this.toastCtrl.create({message:`\u{1F195} New fire evacuation center: ${e.name}`,duration:5e3,color:"danger",position:"top"}).then(k=>k.present())),m.addTo(this.map),console.log(`\u{1F525} Added fire marker: ${e.name}`)}}),console.log("\u{1F525} Auto-routing to 2 nearest fire centers..."),yield this.routeToTwoNearestCenters(),this.evacuationCenters.length>0){let e=p.latLngBounds([]);e.extend([t,n]),this.evacuationCenters.forEach(i=>{e.extend([Number(i.latitude),Number(i.longitude)])}),this.map.fitBounds(e,{padding:[50,50]})}})}calculateDistance(t,n,e,i){let m=t*Math.PI/180,C=e*Math.PI/180,f=(e-t)*Math.PI/180,k=(i-n)*Math.PI/180,I=Math.sin(f/2)*Math.sin(f/2)+Math.cos(m)*Math.cos(C)*Math.sin(k/2)*Math.sin(k/2);return 6371e3*(2*Math.atan2(Math.sqrt(I),Math.sqrt(1-I)))}routeToTwoNearestCenters(){return M(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F525} FIRE MAP: No user location or evacuation centers available");return}try{console.log("\u{1F525} FIRE MAP: Finding 2 nearest fire centers...");let t=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(t.length===0){yield(yield this.toastCtrl.create({message:"No fire evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(t),yield this.calculateRoutes(t),yield(yield this.toastCtrl.create({message:`\u{1F525} Showing routes to ${t.length} nearest fire centers`,duration:4e3,color:"danger"})).present()}catch(t){console.error("\u{1F525} FIRE MAP: Error calculating routes",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}getTwoNearestCenters(t,n){return this.evacuationCenters.map(i=>E(N({},i),{distance:this.calculateDistance(t,n,Number(i.latitude),Number(i.longitude))})).sort((i,s)=>i.distance-s.distance).slice(0,2)}addPulsingMarkers(t){this.nearestMarkers.forEach(n=>this.map.removeLayer(n)),this.nearestMarkers=[],t.forEach((n,e)=>{let i=Number(n.latitude),s=Number(n.longitude);if(!isNaN(i)&&!isNaN(s)){let m=p.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <div class="pulse" style="background-color: #dc3545"></div>
              <img src="assets/forFire.png" class="marker-icon" />
              <div class="marker-label">${e+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),C=p.marker([i,s],{icon:m});C.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${e+1}</h3>
            <h4>${n.name}</h4>
            <p><strong>Type:</strong> Fire</p>
            <p><strong>Distance:</strong> ${(n.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${n.capacity||"N/A"}</p>
          </div>
        `),C.addTo(this.map),this.nearestMarkers.push(C)}})}calculateRoutes(t){return M(this,null,function*(){if(this.userLocation){this.routeLayer=p.layerGroup().addTo(this.map);for(let n=0;n<t.length;n++){let e=t[n],i=Number(e.latitude),s=Number(e.longitude);if(!isNaN(i)&&!isNaN(s))try{console.log(`\u{1F525} FIRE MAP: Creating Mapbox route to center ${n+1}: ${e.name}`);let m=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),C=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,s,i,m);if(C&&C.routes&&C.routes.length>0){let f=C.routes[0];p.polyline(f.geometry.coordinates.map(I=>[I[1],I[0]]),{color:"#dc3545",weight:4,opacity:.8,dashArray:n===0?void 0:"10, 10"}).addTo(this.routeLayer),n===0&&(this.routeTime=f.duration,this.routeDistance=f.distance),console.log(`\u2705 FIRE MAP: Added Mapbox route to ${e.name} (${(f.distance/1e3).toFixed(2)}km, ${(f.duration/60).toFixed(0)}min)`)}}catch(m){console.error(`\u{1F525} Error calculating Mapbox route to center ${n+1}:`,m),p.polyline([[this.userLocation.lat,this.userLocation.lng],[i,s]],{color:"#dc3545",weight:4,opacity:.8,dashArray:n===0?void 0:"10, 10"}).addTo(this.routeLayer),console.log(`\u26A0\uFE0F FIRE MAP: Used fallback straight-line route to ${e.name}`)}}}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers.forEach(t=>{this.map.removeLayer(t)}),this.nearestMarkers=[]}showNavigationPanel(t){return M(this,null,function*(){this.selectedCenter=t,this.selectedTransportMode="walking",this.routeInfo={},this.showRouteFooter=!0,yield this.calculateAllRoutes(t)})}calculateAllRoutes(t){return M(this,null,function*(){if(!this.userLocation)return;let n=["walking","cycling","driving"];for(let e of n)try{let i=this.mapboxRouting.convertTravelModeToProfile(e),s=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(t.longitude),Number(t.latitude),i);if(s&&s.routes&&s.routes.length>0){let m=s.routes[0];this.routeInfo[e]={duration:m.duration,distance:m.distance}}}catch(i){console.error(`\u{1F525} Error calculating ${e} route:`,i)}})}navigateWithMode(t){return M(this,null,function*(){if(!(!this.selectedCenter||!this.userLocation)){this.selectedTransportMode=t,this.clearRoutes();try{let n=this.mapboxRouting.convertTravelModeToProfile(t),e=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,Number(this.selectedCenter.longitude),Number(this.selectedCenter.latitude),n);if(e&&e.routes&&e.routes.length>0){let i=e.routes[0],s="#dc3545";this.routeLayer=p.layerGroup().addTo(this.map);let m=p.polyline(i.geometry.coordinates.map(f=>[f[1],f[0]]),{color:s,weight:5,opacity:.8});m.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`\u{1F525} Route: ${(i.distance/1e3).toFixed(2)}km, ${(i.duration/60).toFixed(0)}min via ${t}`,duration:4e3,color:"danger"})).present(),this.map.fitBounds(m.getBounds(),{padding:[50,50]})}}catch(n){console.error("\u{1F525} Error showing route:",n),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}}})}closeNavigationPanel(){this.selectedCenter=null,this.selectedTransportMode=null,this.routeInfo={},this.clearRoutes()}goBack(){this.router.navigate(["/tabs/home"])}onTravelModeChange(t){let n=t.detail.value;this.changeTravelMode(n)}changeTravelMode(t){return M(this,null,function*(){this.travelMode=t,yield(yield this.toastCtrl.create({message:`\u{1F525} Travel mode changed to ${t}`,duration:2e3,color:"danger"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}downloadMap(){return M(this,null,function*(){if(!this.map){yield(yield this.toastCtrl.create({message:"Map not loaded yet. Please wait and try again.",duration:3e3,color:"warning"})).present();return}try{yield this.enhancedDownload.downloadMapWithRoutes("fire-map",this.map,"Fire",!0)}catch(t){console.error("Enhanced download error:",t),yield(yield this.toastCtrl.create({message:"Failed to download map. Please try again.",duration:3e3,color:"danger"})).present()}})}showAllCenters(){this.showAllCentersPanel=!0}closeAllCentersPanel(){this.showAllCentersPanel=!1}routeToNearestCenters(){return M(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){yield(yield this.toastCtrl.create({message:"Unable to calculate routes. Please ensure location is available.",duration:3e3,color:"warning"})).present();return}try{yield(yield this.toastCtrl.create({message:"\u{1F525} Calculating routes to nearest fire centers...",duration:2e3,color:"danger"})).present(),yield this.routeToTwoNearestCenters()}catch(t){console.error("\u{1F525} Error routing to nearest centers:",t),yield(yield this.toastCtrl.create({message:"Failed to calculate routes. Please try again.",duration:3e3,color:"danger"})).present()}})}calculateDistanceInKm(t){if(!this.userLocation)return"N/A";let n=Number(t.latitude),e=Number(t.longitude);return isNaN(n)||isNaN(e)?"N/A":(this.calculateDistance(this.userLocation.lat,this.userLocation.lng,n,e)/1e3).toFixed(1)}selectCenterFromList(t){this.closeAllCentersPanel(),this.showNavigationPanel(t);let n=Number(t.latitude),e=Number(t.longitude);this.map.setView([n,e],15)}formatTime(t){return t?`${Math.round(t/60)} min`:"--"}ionViewWillLeave(){this.clearRoutes(),this.isRealTimeNavigationActive&&this.osmRouting.stopRealTimeRouting(),this.map&&this.map.remove()}startRealTimeNavigation(t){console.log("\u{1F9ED} Starting real-time navigation to fire center:",t.name),this.navigationDestination={lat:Number(t.latitude),lng:Number(t.longitude),name:t.name},this.isRealTimeNavigationActive=!0,this.toastCtrl.create({message:`\u{1F9ED} Real-time navigation started to ${t.name}`,duration:3e3,color:"primary"}).then(n=>n.present())}onNavigationRouteUpdated(t){console.log("\u{1F504} Fire map navigation route updated"),this.currentNavigationRoute=t,this.updateMapWithNavigationRoute(t)}onNavigationStopped(){console.log("\u23F9\uFE0F Fire map real-time navigation stopped"),this.isRealTimeNavigationActive=!1,this.navigationDestination=null,this.currentNavigationRoute=null,this.clearNavigationRoute(),this.toastCtrl.create({message:"\u23F9\uFE0F Navigation stopped",duration:2e3,color:"medium"}).then(t=>t.present())}updateMapWithNavigationRoute(t){if(this.clearNavigationRoute(),t.geometry&&t.geometry.coordinates){let n=this.osmRouting.convertToGeoJSON(t),e=p.geoJSON(n,{style:{color:"#dc3545",weight:6,opacity:.8,dashArray:"10, 5"}}).addTo(this.map);e.isNavigationRoute=!0}}clearNavigationRoute(){this.map.eachLayer(t=>{t.isNavigationRoute&&this.map.removeLayer(t)})}static{this.\u0275fac=function(n){return new(n||r)}}static{this.\u0275cmp=R({type:r,selectors:[["app-fire-map"]],standalone:!0,features:[D],decls:67,vars:24,consts:[[3,"translucent"],["color","danger"],["slot","start"],["fill","clear",3,"click"],["name","arrow-back-outline","color","light"],[3,"fullscreen"],["id","fire-map",2,"height","100%","width","100%"],[1,"map-controls"],["fill","clear",1,"control-btn",3,"click"],["src","assets/home-insuranceForFire.png","alt","All Centers",1,"control-icon"],["src","assets/downloadForFire.png","alt","Download",1,"control-icon"],["src","assets/compassForFire.png","alt","Route to Nearest",1,"control-icon"],[1,"travel-mode-selector"],[3,"ngModelChange","ionChange","ngModel"],["value","walking"],["name","walk-outline"],["value","cycling"],["name","bicycle-outline"],["value","driving"],["name","car-outline"],["class","route-info",4,"ngIf"],[1,"all-centers-panel"],[1,"panel-content"],[1,"panel-header"],[1,"header-info"],["name","close"],[1,"centers-list"],["class","center-item",3,"click",4,"ngFor","ngForOf"],[1,"all-centers-overlay",3,"click"],[3,"destination","travelMode","autoStart","routeUpdated","navigationStopped",4,"ngIf"],[1,"navigation-panel"],[4,"ngIf"],["class","center-details",4,"ngIf"],["class","transport-options",4,"ngIf"],[1,"route-footer"],[1,"footer-content"],[1,"route-summary"],[1,"transport-icon"],[3,"name"],[1,"route-details"],[1,"destination"],["class","route-info-footer",4,"ngIf"],[1,"footer-actions"],["fill","solid","color","danger","size","small",3,"click"],[1,"navigation-overlay",3,"click"],[1,"route-info"],[1,"route-header"],["name","time-outline","color","danger"],[1,"route-item"],["name","location-outline"],[1,"center-item",3,"click"],[1,"center-info"],[1,"address"],[1,"center-details"],["class","distance",4,"ngIf"],[1,"capacity"],[1,"center-actions"],["name","chevron-forward-outline"],[1,"distance"],[3,"routeUpdated","navigationStopped","destination","travelMode","autoStart"],[1,"detail-item"],["name","people-outline"],["class","detail-item",4,"ngIf"],["name","navigate-outline"],[1,"transport-options"],[1,"transport-option",3,"click"],[1,"transport-info"],[1,"mode"],["class","details",4,"ngIf"],[1,"transport-action"],[1,"details"],[1,"route-info-footer"],[1,"time"]],template:function(n,e){n&1&&(o(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),_("click",function(){return e.goBack()}),g(4,"ion-icon",4),a()()()(),o(5,"ion-content",5),g(6,"div",6),o(7,"div",7)(8,"ion-button",8),_("click",function(){return e.showAllCenters()}),g(9,"img",9),a(),o(10,"ion-button",8),_("click",function(){return e.downloadMap()}),g(11,"img",10),a(),o(12,"ion-button",8),_("click",function(){return e.routeToNearestCenters()}),g(13,"img",11),a()(),o(14,"div",12)(15,"ion-segment",13),z("ngModelChange",function(s){return A(e.travelMode,s)||(e.travelMode=s),s}),_("ionChange",function(s){return e.onTravelModeChange(s)}),o(16,"ion-segment-button",14),g(17,"ion-icon",15),o(18,"ion-label"),l(19,"Walk"),a()(),o(20,"ion-segment-button",16),g(21,"ion-icon",17),o(22,"ion-label"),l(23,"Cycle"),a()(),o(24,"ion-segment-button",18),g(25,"ion-icon",19),o(26,"ion-label"),l(27,"Drive"),a()()()(),P(28,Ct,16,3,"div",20),o(29,"div",21)(30,"div",22)(31,"div",23)(32,"div",24)(33,"h3"),l(34,"\u{1F525} Fire Evacuation Centers"),a(),o(35,"p"),l(36),a()(),o(37,"ion-button",3),_("click",function(){return e.closeAllCentersPanel()}),g(38,"ion-icon",25),a()(),o(39,"div",26),P(40,Pt,12,4,"div",27),a()()(),o(41,"div",28),_("click",function(){return e.closeAllCentersPanel()}),a(),P(42,vt,1,3,"app-real-time-navigation",29),o(43,"div",30)(44,"div",22)(45,"div",23)(46,"div",24)(47,"h3"),l(48),a(),P(49,xt,2,1,"p",31),a(),o(50,"ion-button",3),_("click",function(){return e.closeNavigationPanel()}),g(51,"ion-icon",25),a()(),P(52,wt,10,3,"div",32)(53,Nt,33,12,"div",33),a()(),o(54,"div",34)(55,"div",35)(56,"div",36)(57,"div",37),g(58,"ion-icon",38),a(),o(59,"div",39)(60,"div",40),l(61),a(),P(62,Et,5,2,"div",41),a()(),o(63,"div",42)(64,"ion-button",43),_("click",function(){return e.startRealTimeNavigation(e.selectedCenter)}),l(65," Start "),a()()()(),o(66,"div",44),_("click",function(){return e.closeNavigationPanel()}),a()()),n&2&&(d("translucent",!0),c(5),d("fullscreen",!0),c(10),L("ngModel",e.travelMode),c(13),d("ngIf",e.routeTime>0&&e.routeDistance>0),c(),O("show",e.showAllCentersPanel),c(7),x("",e.evacuationCenters.length," centers available"),c(4),d("ngForOf",e.evacuationCenters),c(),O("show",e.showAllCentersPanel),c(),d("ngIf",e.navigationDestination),c(),O("show",e.selectedCenter),c(5),x("\u{1F525} ",e.selectedCenter==null?null:e.selectedCenter.name,""),c(),d("ngIf",e.selectedCenter),c(3),d("ngIf",e.selectedCenter),c(),d("ngIf",e.selectedCenter),c(),O("show",e.showRouteFooter&&e.selectedCenter),c(4),d("name",e.selectedTransportMode==="walking"?"walk-outline":e.selectedTransportMode==="cycling"?"bicycle-outline":"car-outline"),c(3),y(e.selectedCenter==null?null:e.selectedCenter.name),c(),d("ngIf",e.routeInfo[e.selectedTransportMode||"walking"]),c(4),O("show",e.selectedCenter))},dependencies:[st,q,H,Q,X,Z,tt,et,nt,ot,it,at,Y,W,$,V,J,G,K,_t],styles:["#fire-map[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.map-controls[_ngcontent-%COMP%]{position:absolute;top:80px;right:10px;z-index:1000;display:flex;flex-direction:column;gap:8px}.control-btn[_ngcontent-%COMP%]{--background: rgba(255, 255, 255, .95);--color: #dc3545;--border-radius: 12px;width:50px;height:50px;box-shadow:0 4px 12px #00000026;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(220,53,69,.2)}.control-btn[_ngcontent-%COMP%]:hover{--background: rgba(220, 53, 69, .1);transform:translateY(-2px);box-shadow:0 6px 16px #0003}.control-icon[_ngcontent-%COMP%]{width:28px;height:28px;object-fit:contain}.all-centers-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:380px;height:100vh;background:#fff;box-shadow:-4px 0 20px #00000026;z-index:1500;transition:right .3s cubic-bezier(.25,.46,.45,.94);display:flex;flex-direction:column}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.all-centers-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;padding:20px;overflow:hidden}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px;padding-bottom:16px;border-bottom:2px solid var(--ion-color-danger-tint)}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:20px;font-weight:700;color:var(--ion-color-danger);line-height:1.2}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);font-weight:500}.all-centers-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding-right:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:var(--ion-color-light);border-radius:2px}.all-centers-panel[_ngcontent-%COMP%]   .centers-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:var(--ion-color-danger-tint);border-radius:2px}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:12px;background:#fff;border-radius:12px;border:1px solid var(--ion-color-light);box-shadow:0 2px 8px #00000014;cursor:pointer;transition:all .2s ease}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 16px #dc354526;border-color:var(--ion-color-danger-tint)}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]{flex:1}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:var(--ion-color-dark);line-height:1.3}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .address[_ngcontent-%COMP%]{margin:0 0 8px;font-size:13px;color:var(--ion-color-medium);line-height:1.4}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%], .all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-info[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .capacity[_ngcontent-%COMP%]{font-size:12px;font-weight:500;padding:2px 8px;border-radius:8px;background:var(--ion-color-danger-tint);color:var(--ion-color-danger);width:fit-content}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]{margin-left:12px}.all-centers-panel[_ngcontent-%COMP%]   .center-item[_ngcontent-%COMP%]   .center-actions[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-medium)}.all-centers-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#0006;z-index:1400;opacity:0;visibility:hidden;transition:all .3s ease}.all-centers-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: #dc3545;--color: white;--border-width: 0}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;font-size:18px}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.navigation-panel[_ngcontent-%COMP%]{position:fixed;top:0;right:-400px;width:380px;height:100vh;background:#fff;box-shadow:-4px 0 20px #00000026;z-index:1600;transition:right .3s cubic-bezier(.25,.46,.45,.94);display:flex;flex-direction:column}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;padding:20px;overflow:hidden}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px;padding-bottom:16px;border-bottom:2px solid var(--ion-color-danger-tint)}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;font-size:20px;font-weight:700;color:var(--ion-color-danger);line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:var(--ion-color-medium);font-weight:500;line-height:1.4}.navigation-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);margin:0}.navigation-panel[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]{margin-bottom:24px}.navigation-panel[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:8px 0;font-size:14px;color:var(--ion-color-dark)}.navigation-panel[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-danger);min-width:18px}.navigation-panel[_ngcontent-%COMP%]   .center-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{line-height:1.4}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]{flex:1;overflow-y:auto}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 16px;font-size:16px;font-weight:600;color:var(--ion-color-dark)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;margin-bottom:12px;background:#fff;border:2px solid var(--ion-color-light);border-radius:12px;cursor:pointer;transition:all .2s ease}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]:hover{border-color:var(--ion-color-danger-tint);background:#dc35450d;transform:translateY(-1px);box-shadow:0 4px 12px #0000001a}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option.selected[_ngcontent-%COMP%]{border-color:var(--ion-color-danger);background:#dc35451a;box-shadow:0 4px 16px #dc354533}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:44px;height:44px;border-radius:50%;background:var(--ion-color-danger-tint);display:flex;align-items:center;justify-content:center;margin-right:16px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--ion-color-danger)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-info[_ngcontent-%COMP%]{flex:1}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-info[_ngcontent-%COMP%]   .mode[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-dark);margin-bottom:4px;line-height:1.2}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-info[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]{font-size:13px;color:var(--ion-color-medium);font-weight:500}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-action[_ngcontent-%COMP%]{margin-left:12px}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option[_ngcontent-%COMP%]   .transport-action[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px;color:var(--ion-color-medium)}.navigation-panel[_ngcontent-%COMP%]   .transport-options[_ngcontent-%COMP%]   .transport-option.selected[_ngcontent-%COMP%]   .transport-action[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:var(--ion-color-danger)}.navigation-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#0006;z-index:1500;opacity:0;visibility:hidden;transition:all .3s ease}.navigation-overlay.show[_ngcontent-%COMP%]{opacity:1;visibility:visible}.route-footer[_ngcontent-%COMP%]{position:fixed;bottom:-100px;left:0;right:0;background:#fff;border-top:1px solid var(--ion-color-light);box-shadow:0 -4px 20px #0000001a;z-index:1500;transition:bottom .3s ease-in-out}.route-footer.show[_ngcontent-%COMP%]{bottom:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:16px 20px;display:flex;align-items:center;justify-content:space-between}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;flex:1}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:var(--ion-color-danger-tint);display:flex;align-items:center;justify-content:center}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:var(--ion-color-danger)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{flex:1}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .destination[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:var(--ion-color-dark);margin-bottom:2px;line-height:1.2}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:var(--ion-color-danger)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:12px;color:var(--ion-color-medium)}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .footer-actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 20px;height:36px;font-weight:600}.transport-info[_ngcontent-%COMP%]{flex:1}.transport-info[_ngcontent-%COMP%]   .mode[_ngcontent-%COMP%]{display:block;font-weight:600;font-size:1rem;margin-bottom:2px}.transport-info[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]{display:block;font-size:.85rem;color:#666}.leaflet-container[_ngcontent-%COMP%]{height:100%;width:100%}.leaflet-popup-content[_ngcontent-%COMP%]{margin:8px 12px;line-height:1.4}.evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 10px;color:#dc3545;font-size:1.1rem}.evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:5px 0;font-size:.9rem}.evacuation-popup[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#333}@media (max-width: 768px){.map-controls[_ngcontent-%COMP%]{top:70px;right:8px}.all-centers-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.all-centers-panel.show[_ngcontent-%COMP%]{right:0}.navigation-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.navigation-panel.show[_ngcontent-%COMP%]{right:0}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{padding:12px 16px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]{gap:10px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]{width:36px;height:36px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .transport-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .destination[_ngcontent-%COMP%]{font-size:14px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%]{font-size:13px}.route-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .route-summary[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]   .route-info-footer[_ngcontent-%COMP%]   .distance[_ngcontent-%COMP%]{font-size:11px}}.travel-mode-selector[_ngcontent-%COMP%]{position:absolute;top:20px;left:20px;z-index:1000;background:#fffffff2;border-radius:12px;padding:8px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{--background: transparent;min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--color-checked: var(--ion-color-danger);--indicator-color: var(--ion-color-danger);min-height:40px}.travel-mode-selector[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:2px}.travel-mode-selector[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-info[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;z-index:1000;max-width:200px}.route-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;border-radius:12px;background:#fffffff2}.route-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-danger);margin-bottom:8px}.route-info[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;color:var(--ion-color-dark)}.route-info[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;color:var(--ion-color-danger)}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse-container[_ngcontent-%COMP%]{position:relative;width:50px;height:50px;display:flex;align-items:center;justify-content:center}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse[_ngcontent-%COMP%]{position:absolute;width:40px;height:40px;border-radius:50%;animation:_ngcontent-%COMP%_pulse 2s infinite;opacity:.6}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-icon[_ngcontent-%COMP%]{width:30px;height:30px;z-index:2;position:relative}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-label[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:#dc3545;color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;z-index:3;border:2px solid white}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.2);opacity:.4}to{transform:scale(.8);opacity:.8}}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-danger);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup.nearest-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#dc3545;font-size:18px}"]})}}return r})();export{Xt as FireMapPage};
